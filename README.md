## 1. 项目概述与目标

本项目旨在开发一款塔防游戏，遵循面向对象的设计原则和现代C++的编程范式。开发周期约为一个月，由单人完成。核心目标是在满足所有基本功能和部分扩展功能的前提下，尽可能降低开发难度，并力求获得较高的课程分数。

## 2. 开发环境与工具

* **IDE**:
  * **Qt Creator** (6.0或更高版本) - 主要用于GUI设计和Qt相关代码
  * **VS Code** (最新版) - 用于核心逻辑开发，配合CMake Tools插件

* **构建系统**:
  * **CMake** (3.15或更高版本) - 确保两个IDE都能顺利构建项目
  * **Ninja** (可选) - 更快的构建工具，作为CMake的生成器

* **图形库**:
  * **Qt** (6.x版本) - 用于GUI开发，同时利用其信号槽机制
  * **可选备选**: SFML或SDL2，如果决定不使用Qt

* **C++ 标准**:
  * **C++17** 或 **C++20** (如果编译器支持完善)

* **版本控制**:
  * **Git** - 使用GitLab或GitHub进行代码托管
  * **分支策略**:
    * `main` - 稳定分支，保持可构建状态
    * `develop` - 开发分支，整合功能
    * `feature/*` - 功能分支，每个新功能
    * `bugfix/*` - 修复分支，解决问题

* **代码风格与工具**:
  * **clang-format** (12.0或更高) - 代码格式化
  * **clang-tidy** (12.0或更高) - 静态分析
  * **cpplint** (可选) - 附加的代码风格检查

* **测试框架**:
  * **Google Test** (1.10或更高) - 用于单元测试核心逻辑
  * **Catch2** (2.x或3.x) - 轻量级替代方案

* **调试工具**:
  * **GDB/LLDB** - 命令行调试
  * **Valgrind** (仅Linux) - 内存泄漏检测
  * **Address Sanitizer** - 运行时内存错误检测
  * **QDebug** - Qt日志系统

* **文档工具**:
  * **Doxygen** - 从代码注释生成文档
  * **Markdown** - 项目文档和README
  * **PlantUML/Mermaid** - 图表生成工具，用于设计文档

* **项目管理**:
  * **GitLab/GitHub Issues** - Bug跟踪和特性请求
  * **GitLab/GitHub Boards** - 看板式任务管理

## 3. 项目目录结构 (CMake友好)

建议采用以下更规范的、遵循现代CMake实践的目录结构：

```

TowerDefense/
├── CMakeLists.txt # 主CMake配置文件
├── .gitignore
├── README.md
├── LICENSE # (可选)
├── cmake/ # 自定义CMake模块和Find脚本
├── include/
│ └── TowerDefense/ # 项目头文件 (按模块组织)
│ ├── Core/
│ ├── Gui/
│ ├── Utils/
│ └── GameLogic/ # (根据实际模块划分)
├── src/ # 源代码 (.cpp 文件)
│ ├── Core/ # 核心逻辑 (游戏状态、基础实体等)
│ ├── GameLogic/ # 游戏具体逻辑 (单位、地图、战斗、词缀等)
│ ├── Gui/ # GUI相关代码 (窗口、控件、渲染逻辑)
│ └── Utils/ # 工具类 (数学计算、文件处理等)
├── apps/ # (可选) 可执行程序入口 (如果main.cpp移到这里)
│ └── TowerDefenseGame/
│ └── main.cpp
├── assets/ # 游戏资源 (打包时与可执行文件一起安装)
│ ├── images/
│ ├── sounds/
│ ├── fonts/
│ └── data/ # 游戏数据 (地图、配置等，替代原顶层data/)
│ ├── maps/
│ └── game_config/
├── tests/ # 单元测试代码 (如使用Google Test, Catch2)
│ └── CMakeLists.txt
├── extern/ # (或 third_party/) 第三方库 (Git submodule或FetchContent管理)
├── build/ # 编译输出目录 (非源码构建，由.gitignore忽略)
└── doc/ # 项目文档


```
* **构建系统**：强烈推荐使用 **CMake**。在根目录 `CMakeLists.txt` 中配置项目选项和工具链，然后在 `src/`, `tests/` 等子目录中单独添加 `CMakeLists.txt` 来组织模块。

## 4. 代码风格与工具

* **C++标准**：使用 **C++17或更高版本**。在CMake中设置 `target_compile_features(... cxx_std_17 PUBLIC)`。
* **编译警告**：开启尽可能多的编译警告，例如 GCC/Clang 使用 `-Wall -Wextra -Wpedantic`。将警告视为错误 (`-Werror`) 在开发阶段可以帮助尽早发现问题。
* **代码格式化**：使用统一的编码规范 (可参考 Google C++ Style Guide, LLVM Coding Standards 等)，并配置 **`clang-format`** 自动格式化代码。
* **静态分析**：集成 **`clang-tidy`** 或 Cppcheck 等静态分析工具，进行代码检查，捕获常见错误和潜在性能问题。
* **版本控制**：使用 Git。在 `.gitignore` 中忽略编译输出、IDE配置文件等。
* **单元测试**：为核心模块编写单元测试，使用 Google Test 或 Catch2 等框架，放在 `tests/` 目录下。

## 5. 核心系统设计 (面向对象)

### 5.0 设计模式应用

设计模式是解决特定问题的通用方案，在塔防游戏中，合理应用设计模式能提高代码复用性、可维护性和扩展性。以下是本项目中应用的主要设计模式：

#### 创建型模式

* **单例模式（Singleton）**：用于 `GameController` 和 `GameConfig` 等需要全局访问的对象。确保这些类在整个游戏生命周期中只有一个实例，并提供全局访问点。

* **工厂方法模式（Factory Method）**：使用工厂方法创建不同类型的单位（塔、敌人）和词缀。例如：
  ```cpp
  class UnitFactory {
  public:
      virtual ~UnitFactory() = default;
      virtual std::unique_ptr<Unit> createUnit() = 0;
  };

  class TowerFactory : public UnitFactory {
  public:
      std::unique_ptr<Unit> createUnit() override {
          return std::make_unique<Tower>(...);
      }
  };

  class EnemyFactory : public UnitFactory {
  public:
      std::unique_ptr<Unit> createUnit() override {
          return std::make_unique<Enemy>(...);
      }
  };
  ```

* **建造者模式（Builder）**：用于构建复杂的地图对象或配置复杂的单位。如：
  ```cpp
  class MapBuilder {
  public:
      MapBuilder& setSize(int width, int height);
      MapBuilder& addPath(const Path& path);
      MapBuilder& setTileAt(int x, int y, TileType type);
      std::unique_ptr<Map> build();
  private:
      std::unique_ptr<Map> m_map;
  };
  ```

#### 结构型模式

* **组合模式（Composite）**：用于管理复杂的UI组件层次结构，例如主窗口中包含多个面板。

* **装饰器模式（Decorator）**：将词缀系统设计为装饰器模式，动态地为单位添加新功能：
  ```cpp
  class UnitDecorator : public Unit {
  protected:
      Unit* m_decoratedUnit;
  public:
      UnitDecorator(Unit* unit) : m_decoratedUnit(unit) {}
      void update(float deltaTime) override {
          // 添加额外行为
          m_decoratedUnit->update(deltaTime);
      }
  };

  class FrozenDecorator : public UnitDecorator {
  public:
      FrozenDecorator(Unit* unit) : UnitDecorator(unit) {}
      void update(float deltaTime) override {
          // 减慢单位移动速度
          UnitDecorator::update(deltaTime * 0.5f);
      }
  };
  ```

* **代理模式（Proxy）**：用于延迟加载资源，如游戏贴图等大型资源。

#### 行为型模式

* **观察者模式（Observer）**：游戏事件系统**将利用Qt的信号槽机制**实现观察者模式。游戏核心组件（主题）会发出信号，GUI组件或其他监听者（观察者）连接到这些信号以响应事件，从而实现UI与逻辑的解耦通知。 **<-- 修改**
    ```cpp
    // 示例：GameController 发出信号
    class GameController : public QObject { // 需要继承自 QObject
        Q_OBJECT // 需要 Q_OBJECT 宏

    signals:
        void resourceChanged(int newAmount, int changeAmount); // 资源变化信号
        void playerLivesChanged(int newLives, int changeAmount); // 玩家生命值变化信号
        // 其他游戏事件信号...

    public slots: // 可能需要一些槽来接收GUI的输入
        void handlePlaceTower(int towerType, const QPoint& position);
        // ...

    // ... 其他成员和函数
    };

    // 示例：InfoPanelWidget 响应信号
    class InfoPanelWidget : public QWidget // 或其他 Qt 控件基类
    {
        Q_OBJECT // 需要 Q_OBJECT 宏

    public slots: // 槽函数，用于接收信号并更新UI
        void updateResourceDisplay(int newAmount, int changeAmount) {
            // 更新金钱UI显示
        }
         void updateLivesDisplay(int newLives, int changeAmount) {
            // 更新生命值UI显示
        }
        // ...
    };

    // 在某个地方连接信号和槽 (例如 MainWindow 或 QtUIAdapter)
    // connect(&GameController::getInstance(), &GameController::resourceChanged,
    //         infoPanel, &InfoPanelWidget::updateResourceDisplay);
     // connect(&GameController::getInstance(), &GameController::playerLivesChanged,
    //         infoPanel, &InfoPanelWidget::updateLivesDisplay);
    ```

* **策略模式（Strategy）**：用于实现不同类型塔的索敌策略：
  ```cpp
  class TargetingStrategy {
  public:
      virtual ~TargetingStrategy() = default;
      virtual Enemy* selectTarget(const std::vector<Enemy*>& enemies) = 0;
  };

  class FirstEnemyStrategy : public TargetingStrategy {
  public:
      Enemy* selectTarget(const std::vector<Enemy*>& enemies) override {
          // 选择第一个进入范围的敌人
      }
  };

  class StrongestEnemyStrategy : public TargetingStrategy {
  public:
      Enemy* selectTarget(const std::vector<Enemy*>& enemies) override {
          // 选择血量最高的敌人
      }
  };
  ```

* **状态模式（State）**：用于管理游戏状态和单位状态：
  ```cpp
  class GameState {
  public:
      virtual ~GameState() = default;
      virtual void enter(GameController* controller) = 0;
      virtual void update(GameController* controller, float deltaTime) = 0;
      virtual void exit(GameController* controller) = 0;
  };

  class PlayingState : public GameState {
      // 实现...
  };

  class PausedState : public GameState {
      // 实现...
  };
  ```

### 预定义类型与枚举 (Conceptual)

在相应的头文件或一个集中的`Types.h`中定义：

```cpp
// In Types.h or relevant headers
#include <QString>
#include <QPointF>

// 游戏状态
enum class GameState {
    MAIN_MENU,      // 主菜单
    LEVEL_SELECTION,// 关卡选择 (扩展)
    PLAYING,        // 游戏中
    PAUSED,         // 暂停
    GAME_OVER_WIN,  // 游戏胜利
    GAME_OVER_LOSE  // 游戏失败
};

// 单位ID类型
using UnitID = unsigned int;
// 词缀ID类型
using AffixID = unsigned int; // 或者使用QString作为ID

// 单位类型
enum class UnitType {
    TOWER_MELEE,
    TOWER_RANGED_ARROW, // 示例：箭塔
    TOWER_RANGED_CANNON, // 示例：炮塔
    ENEMY_NORMAL,
    ENEMY_FAST,
    ENEMY_ARMORED
    // ... 更多类型
};

// 为了简化，TowerType 和 EnemyType 可以合并到 UnitType，或者分开定义
enum class TowerType { MELEE, RANGED_ARROW, RANGED_CANNON /* ... */ };
enum class EnemyType { NORMAL, FAST, ARMORED /* ... */ };


// 伤害类型
enum class DamageType {
    PHYSICAL,
    MAGICAL,
    PIERCING,
    SIEGE
};

// 词缀类型 (用于区分和管理)
enum class AffixType {
    BERSERK,    // 狂暴 (塔)
    ICE,        // 冰系 (塔)
    AOE,        // 群伤 (塔)
    BLEED,      // 放血 (塔)
    BLINK,      // 闪现 (敌)
    SWIFT,      // 神速 (敌)
    REGENERATION // 回血 (通用或特定)
    // ... 更多词缀
};

// 瓦片/格子类型
enum class TileType {
    INVALID,            // 无效区域
    PATH_HORIZONTAL,    // 路径 (水平)
    PATH_VERTICAL,      // 路径 (垂直)
    PATH_TURN_UP_RIGHT, // 路径 (拐角)
    PATH_TURN_UP_LEFT,
    PATH_TURN_DOWN_RIGHT,
    PATH_TURN_DOWN_LEFT,
    BUILDABLE_GROUND,   // 可建造地面
    OBSTACLE,           // 障碍物
    START_POINT,        // 敌人出兵点
    END_POINT           // 我方基地/目标点
};

// 升级类型 (扩展)
enum class UpgradeType { DAMAGE, RANGE, ATTACK_SPEED };

// 资源类型 (如果游戏中有多种资源)
// enum class ResourceType { GOLD, WOOD, CRYSTAL };

// 用于配置文件中的键
namespace ConfigKeys {
    // Map keys
    const QString MAP_WIDTH = "width";
    const QString MAP_HEIGHT = "height";
    const QString MAP_TILE_SIZE = "tileSize";
    const QString MAP_TILES_DATA = "tilesData"; // e.g., array of tile types
    const QString MAP_PATHS = "paths";
    const QString PATH_WAYPOINTS = "waypoints";
    const QString WAYPOINT_X = "x";
    const QString WAYPOINT_Y = "y";

    // Unit keys
    const QString UNIT_TYPE = "type";
    const QString UNIT_NAME = "name";
    const QString UNIT_HEALTH = "health";
    const QString UNIT_SPRITE = "sprite";
    // Tower specific
    const QString TOWER_COST = "cost";
    const QString TOWER_DAMAGE = "damage";
    const QString TOWER_RANGE = "range";
    const QString TOWER_ATTACK_SPEED = "attackSpeed"; // attacks per second
    // Enemy specific
    const QString ENEMY_SPEED = "moveSpeed"; // pixels per second
    const QString ENEMY_BOUNTY = "bounty";
    const QString ENEMY_BASE_DAMAGE = "baseDamage";
}
```

### 5.1. 核心引擎 (`Core` 模块)

* **`GameController` (优化后使用单例和状态模式，**继承自QObject并利用信号槽**)**
    * **职责**: 驱动整个游戏的核心逻辑循环，管理游戏状态，协调各个子管理器，并作为游戏逻辑层与GUI层交互的主要接口，**通过发出Qt信号通知游戏事件**。
    * **设计模式**: 单例模式，状态模式，外观模式(Facade)，**Qt信号槽 (用于事件通知)**
    * **主要成员**:
        * `static GameController* s_instance` (单例实例)
        * `std::unique_ptr<GameState> m_currentState` (使用状态模式处理不同游戏状态)
        * `std::unique_ptr<Map> m_currentMap`
        * `std::unique_ptr<UnitManager> m_unitManager`
        * `std::unique_ptr<WaveManager> m_waveManager`
        * `std::unique_ptr<ResourceManager> m_resourceManager`
        * `std::unique_ptr<AffixManager> m_affixManager` (阶段二，管理词缀的获取、存储和应用逻辑)
        * `float m_deltaTime` (当前帧的时间增量)
        * `int m_playerLives` (玩家基地生命值)
        * `int m_maxPlayerLives` (玩家最大生命值)
        * `CommandHistory m_commandHistory` (命令模式，用于支持撤销操作)
    * **信号 (Signals)**:
        * `void gameStateChanged(GameState state)`: 游戏状态改变时发出
        * `void unitSpawned(Unit* unit)`: 单位生成时发出
        * `void unitDestroyed(Unit* unit, Unit* killer)`: 单位销毁时发出
        * `void resourceChanged(int newAmount, int changeAmount)`: 资源数量变化时发出
        * `void playerLivesChanged(int newLives, int changeAmount)`: 玩家生命值变化时发出
        * `void waveChanged(int currentWave, int totalWaves)`: 波次信息变化时发出
        * // ... 其他事件信号
    * **主要函数**:
        * `static GameController& getInstance()`: 获取单例实例
        * `void init()`: 初始化游戏系统 (在此设置初始生命值)
        * `void update(float deltaTime)`: 更新游戏逻辑
        * `void changeState(std::unique_ptr<GameState> newState)`: 切换游戏状态 (并在切换后发出 `gameStateChanged` 信号)
        * `void loadMap(const std::string& mapFile)`: 加载地图
        * `bool requestPlaceTower(TowerType type, const QPoint& position)`: 放置塔 (调用 `ResourceManager` 扣除资源；若成功，则在放置塔并更新资源后，发出 `resourceChanged` 信号)
        * `void executeCommand(std::unique_ptr<Command> command)`: 执行命令(命令模式)
        * `void undoLastCommand()`: 撤销上一个命令
        * `void loseLives(int amount)`: 扣除玩家生命值，并在生命值变化后发出 `playerLivesChanged` 信号
        * `int getLives() const`: 获取当前玩家生命值
        * `void checkWinLossConditions()`: 检查游戏胜利或失败条件 (包含玩家生命值检查)
        * `void processEnemyDeath(Enemy* enemy)`: 处理敌人死亡后的逻辑 (例如，**调用 `ResourceManager` 增加金钱，并在更新资源后发出 `resourceChanged` 信号**；通知 `UnitManager` 移除敌人等)
        * // ... 其他公共方法和getter方法，以及可能的槽函数来接收来自GUI的输入
    * **设计优化**:
        * 使用状态模式替代简单的枚举状态，更好地封装状态相关行为
        * **使用Qt信号槽实现事件系统**
        * 使用工厂模式创建不同类型的单位，增强扩展性
        * 使用命令模式支持撤销操作，增强用户体验

* **`GameEventManager` (新增)**
    * **职责**: 实现观察者模式，管理游戏事件的发布和订阅
    * **设计模式**: 观察者模式
    * **主要成员**:
        * `std::vector<GameEventListener*> m_listeners` (不持有所有权)
    * **主要函数**:
        * `void addListener(GameEventListener* listener)`
        * `void removeListener(GameEventListener* listener)`
        * `void notifyGameStateChanged(GameStateType stateType)`
        * `void notifyUnitSpawned(Unit* unit)`
        * `void notifyUnitDestroyed(Unit* unit, Unit* killer)`
        * `void notifyResourceChanged(int newAmount, int changeAmount)`
        * `void notifyWaveChanged(int currentWave, int totalWaves)`
        * `static bool isPointInRect(const QPointF& point, const QRectF& rect)`
        * // ... 其他通知方法

* **`GameEventListener` (新增接口)**
    * **职责**: 定义接收游戏事件的接口
    * **设计模式**: 观察者模式
    * **主要函数**:
        * `virtual void onGameStateChanged(GameStateType stateType) {}`
        * `virtual void onUnitSpawned(Unit* unit) {}`
        * `virtual void onUnitDestroyed(Unit* unit, Unit* killer) {}`
        * `virtual void onResourceChanged(int newAmount, int changeAmount) {}`
        * `virtual void onWaveChanged(int currentWave, int totalWaves) {}`
        * // ... 其他事件处理方法

* **`GameState` (抽象基类，新增)**
    * **职责**: 定义游戏状态的接口
    * **设计模式**: 状态模式
    * **主要函数**:
        * `virtual void enter(GameController* controller) = 0`
        * `virtual void update(GameController* controller, float deltaTime) = 0`
        * `virtual void exit(GameController* controller) = 0`
        * `virtual GameStateType getType() const = 0`

* **`PlayingState`, `PausedState`, `MainMenuState`, `GameOverState` (具体状态类)**
    * **职责**: 实现各游戏状态的具体行为
    * **实现**: 继承自`GameState`，重写其方法

### 5.2. 游戏逻辑 (`GameLogic` 模块)

* **`UnitManager` (优化)**
    * **主要成员**:
        * `std::map<UnitID, std::unique_ptr<Unit>> m_units` (存储所有单位)
        * `std::unique_ptr<UnitFactory> m_towerFactory` (塔工厂)
        * `std::unique_ptr<UnitFactory> m_enemyFactory` (敌人工厂)
        * `GameController* m_gameController` (非拥有引用)
    * **主要函数**:
        * `Unit* createTower(TowerType type, const QPoint& position)`: 使用工厂创建塔
        * `Unit* createEnemy(EnemyType type, int pathIndex)`: 使用工厂创建敌人
        * // ... 其他管理方法

* **`UnitFactory` (新增抽象工厂接口)**
    * **职责**: 定义创建单位的接口
    * **设计模式**: 工厂方法
    * **主要函数**:
        * `virtual std::unique_ptr<Unit> createUnit(UnitType type, const QPointF& position) = 0`

* **`TowerFactory`, `EnemyFactory` (具体工厂)**
    * **职责**: 实现创建特定类型单位的具体逻辑
    * **实现**: 继承自`UnitFactory`，重写其方法

* **`Unit` (抽象基类，优化)**
    * **主要成员**:
        * // ... 原有成员
        * `std::vector<std::unique_ptr<UnitComponent>> m_components` (组件模式)
    * **主要函数**:
        * // ... 原有方法
        * `void addComponent(std::unique_ptr<UnitComponent> component)`
        * `UnitComponent* getComponent(ComponentType type)`
        * `template<typename T> T* getComponentAs()` (类型安全的组件访问)

* **`UnitComponent` (新增抽象组件接口)**
    * **职责**: 定义单位组件的接口
    * **设计模式**: 组件模式
    * **主要函数**:
        * `virtual void initialize(Unit* owner) = 0`
        * `virtual void update(float deltaTime) = 0`
        * `virtual ComponentType getType() const = 0`

* **`HealthComponent`, `MovementComponent`, `CombatComponent` (具体组件)**
    * **职责**: 实现单位特定功能的组件
    * **实现**: 继承自`UnitComponent`，重写其方法

* **`Tower` (继承自 `Unit`，优化)**
    * **主要成员**:
        * // ... 原有成员
        * `std::unique_ptr<TargetingStrategy> m_targetingStrategy` (策略模式)
    * **主要函数**:
        * // ... 原有方法
        * `void setTargetingStrategy(std::unique_ptr<TargetingStrategy> strategy)`

* **`TargetingStrategy` (新增策略接口)**
    * **职责**: 定义塔索敌策略的接口
    * **设计模式**: 策略模式
    * **主要函数**:
        * `virtual Enemy* selectTarget(const std::vector<Enemy*>& enemies, const Tower* tower) = 0`

* **`FirstEnemyStrategy`, `StrongestEnemyStrategy`, `WeakestEnemyStrategy` (具体策略)**
    * **职责**: 实现不同的索敌策略
    * **实现**: 继承自`TargetingStrategy`，重写其方法

* **`Affix` (抽象基类，优化为装饰器模式)**
    * **职责**: 定义词缀效果的接口，使用装饰器模式动态修改单位行为
    * **设计模式**: 装饰器模式
    * **主要函数**:
        * // ... 原有方法
        * `virtual void decorateUnit(Unit* unit)` (装饰单位)
        * `virtual void undecorateUnit(Unit* unit)` (移除装饰)

* **`Enemy` (继承自 `Unit`，优化)**
    * **主要成员**:
        * // ... 原有成员 (如血量、速度、路径相关等)
        * `int m_livesCost` (该类型敌人到达终点时扣除的玩家生命值)
        * // ... 可能还有其他成员

### 5.3. 图形用户界面 (`Gui` 模块 - Qt)

* **`QtUIAdapter` (新增)**
    * **职责**: 作为游戏核心逻辑 (`GameController` 等发出的Qt信号) 与 GUI 组件（接收这些信号的Qt槽）之间的**连接中心**。负责建立信号与槽的连接关系，确保游戏事件能够正确地更新UI。
    * **设计模式**: 适配器模式 (连接不同组件)，**Qt信号槽**
    * **主要成员**:
        * `GameController* m_gameController` (非拥有引用)
        * // 可能持有其他需要连接的 GUI 组件的指针
    * **主要函数**:
        * `void initialize()`
        * `void connectSignalsAndSlots()`: 负责建立具体的 `QObject::connect` 调用
        * // ... 可能根据需要添加一些槽函数来接收来自GUI的简单交互事件，并调用 GameController 的公共方法

* **`MainWindow` (优化)**
    * **主要成员**:
        * // ... 原有成员 (如布局管理器，各种子控件)
        * `QtUIAdapter* m_uiAdapter` (连接中心)
    * **主要函数**:
        * // ... 原有方法 (如 setupUi)
        * `void setupConnections()`: 调用 `m_uiAdapter->connectSignalsAndSlots()`

### 5.4. 工具类 (`Utils` 模块)

* **`Command` (新增抽象命令接口)**
    * **职责**: 定义命令的接口
    * **设计模式**: 命令模式
    * **主要函数**:
        * `virtual void execute() = 0`
        * `virtual void undo() = 0`
        * `virtual std::string getDescription() const = 0`

* **`PlaceTowerCommand`, `SellTowerCommand`, `UpgradeTowerCommand` (具体命令)**
    * **职责**: 实现具体的游戏命令
    * **实现**: 继承自`Command`，重写其方法

* **`CommandHistory` (新增)**
    * **职责**: 管理命令历史，支持撤销操作
    * **设计模式**: 命令模式
    * **主要成员**:
        * `std::stack<std::unique_ptr<Command>> m_undoStack`
        * `std::stack<std::unique_ptr<Command>> m_redoStack`
    * **主要函数**:
        * `void executeCommand(std::unique_ptr<Command> command)`
        * `void undo()`
        * `void redo()`
        * `bool canUndo() const`
        * `bool canRedo() const`

* **`GameConfig` (单例，优化)**
    * **设计模式**: 单例模式
    * **主要成员**:
        * `static GameConfig* s_instance`
        * // ... 原有成员
    * **主要函数**:
        * `static GameConfig& getInstance()`
        * // ... 原有方法

### 5.5. 面向对象原则：

* **封装, 继承, 多态**：核心原则，贯穿于所有模块设计。
* **单一职责原则 (SRP)**：每个类专注于单一功能。例如，`UnitManager`专注于单位管理，`ResourceManager`专注于资源管理。**(可选改进方向：可以将玩家的生命值、分数等信息提取到一个独立的 `PlayerStats` 类中，进一步解耦 `GameController` 的职责)**
* **开放/封闭原则 (OCP)**：系统设计允许通过继承和组合扩展功能，而不修改现有代码。例如，使用工厂模式添加新单位类型，使用装饰器模式添加新词缀效果。
* **李氏替换原则 (LSP)**：子类可以替换基类而不改变程序正确性。例如，任何`Tower`子类都可以在需要`Tower`的地方使用。
* **接口隔离原则 (ISP)**：通过定义专用接口避免类依赖不需要的方法。**(此例不再适用，可以考虑移除或替换为其他例子)**
* **依赖倒置原则 (DIP)**：高层模块不依赖低层模块，两者都依赖抽象。例如，`GameController`依赖`UnitFactory`接口而非具体实现。**(可选的补充说明，解释权衡)**
* **组合优于继承**：使用组件模式和装饰器模式，通过组合实现功能复用，避免复杂的继承层次。

## 6. 分阶段开发计划 (GUI早期集成)

### 阶段一: 核心循环与基础GUI (约1-1.5周)

* **逻辑**:
    * `GameController` 基础 (状态机: MAIN_MENU, PLAYING, PAUSED)，**继承自QObject并定义核心事件的Qt信号**。
    * `GameController` 管理玩家生命值 (`m_playerLives`, `m_maxPlayerLives`)，并在敌人到达终点时根据敌人类型扣除生命值 (`loseLives` 方法)，**扣除后发出 `playerLivesChanged` 信号**。
    * `GameController` 的 `checkWinLossConditions` 方法包含检查玩家生命值是否耗尽的失败条件。
    * `Map` 加载 (硬编码或极简文本格式)，`Path` 定义。
    * `UnitManager` 基础。
    * `Unit`, `Tower` (Melee, Ranged), `Enemy` (Basic) 类骨架，包含基本属性 (HP, pos)。
    * `Enemy` 类包含一个表示其到达终点时扣除玩家生命值的属性 (`m_livesCost`)。
    * `ResourceManager` 基础 (金钱)，**资源变化后发出 `resourceChanged` 信号 (可能从 `ResourceManager` 发出，或通过 `GameController` 代理)**。
    * 塔放置逻辑 (检查格子类型，扣钱)。
    * 敌人沿路径移动 (简单插值)。
    * 塔自动索敌 (简单范围检测) 并攻击 (仅逻辑标记，无视觉)。
    * 敌人受击扣血，死亡后从 `UnitManager` 移除。
    * 简单的胜负条件 (如敌人到达终点，玩家生命值减少)。
* **GUI (Qt)**:
    * `MainWindow` 框架，包含一个 `GameView` 区域。
    * `GameView`:
        * 绘制地图格子 (不同颜色代表不同类型)。
        * 绘制路径。
        * 绘制塔和敌人 (用简单的 `QColor` 和 `QRectF` 或 `QEllipse`)。
        * 响应鼠标点击，获取地图格子坐标 (可能通过信号通知 `GameController` 的槽函数)。
    * `TowerPaletteWidget`: 显示1-2种塔的按钮 (图标可选，文字即可)。点击按钮后，`GameView` 进入放置模式 (点击事件通过信号通知 `GameController` 的槽函数)。
    * `ControlPanelWidget`: "开始游戏" 按钮，"下一波" (点击事件通过信号通知 `GameController` 的槽函数)。
    * `InfoPanelWidget`: **定义槽函数接收来自 `GameController` (或其他核心类) 的信号**，显示玩家金钱、生命值。
    * `GameController` 发出 ~~`gameNeedsRedraw` 信号~~ (可以继续使用，或者更细化为单位位置变化等信号)，`GameView` 连接并调用 `update()` 刷新。
    * **创建 `QtUIAdapter` 负责连接核心逻辑发出的信号和GUI组件接收信号的槽函数。**
* **接口重点**:
    * `GameController` (作为 QObject) 定义和发出核心游戏事件的Qt信号。
    * GUI 组件 (如 `InfoPanelWidget`, `GameView` 的槽函数) 接收并响应这些信号。
    * `QtUIAdapter` 负责建立信号与槽的连接。
    * `GameView` 从 `GameController` (或其管理的模块) 获取渲染所需的数据。
    * `GameController` 提供获取玩家生命值的方法 (`getLives()`)。
    * ~~`GameController` 通过 `GameEventManager` 通知玩家生命值变化事件。~~

*   **建议的文件编写与实现顺序**:
    *(此顺序考虑了文件间的依赖关系，建议按此顺序逐步创建和实现)*
    1.  `include/TowerDefense/Types.h`: 定义全局通用的枚举、类型别名和配置键。
    2.  `include/TowerDefense/Core/ResourceManager.hpp` 和 `src/Core/ResourceManager.cpp`: 实现资源（金钱）管理逻辑。
    3.  `include/TowerDefense/GameLogic/Unit.hpp` 和 `src/GameLogic/Unit.cpp`: 定义抽象的单位基类。
    4.  `include/TowerDefense/GameLogic/Map.hpp` 和 `src/GameLogic/Map.cpp`: 实现地图结构和路径管理（Phase 1 简化加载）。
    5.  `include/TowerDefense/GameLogic/UnitManager.hpp` 和 `src/GameLogic/UnitManager.cpp`: 实现单位的添加、移除和整体管理。
    6.  `include/TowerDefense/GameLogic/UnitFactory.hpp`: 定义抽象的单位工厂接口。
    7.  `include/TowerDefense/GameLogic/Enemy.hpp` 和 `src/GameLogic/Enemy.cpp`: 实现敌人基类（继承 `Unit`），包含移动和生命消耗属性。
    8.  `include/TowerDefense/GameLogic/EnemyFactory.hpp` 和 `src/GameLogic/EnemyFactory.cpp`: 实现具体的敌人工厂，负责创建不同类型的敌人实例。
    9.  `include/TowerDefense/GameLogic/Tower.hpp` 和 `src/GameLogic/Tower.cpp`: 实现塔基类（继承 `Unit`），包含基本的攻击逻辑。
    10. `include/TowerDefense/GameLogic/TowerFactory.hpp` 和 `src/GameLogic/TowerFactory.cpp`: 实现具体的塔工厂，负责创建不同类型的塔实例。
    11. `src/Core/GameController.cpp`: 实现 `GameController` 的核心游戏循环和逻辑，连接各管理器和工厂，**并在状态/资源/生命值变化时发出 Qt 信号**。
    12. **GUI 模块文件** (`include/TowerDefense/Gui/` 和 `src/Gui/` 下的各个文件)：
        *   `MainWindow` (框架)
        *   `GameView` (地图和单位渲染，处理鼠标输入并发出信号)
        *   `InfoPanelWidget` (显示资源和生命值，定义槽函数接收信号)
        *   `TowerPaletteWidget` (塔选择面板，点击发出信号)
        *   `ControlPanelWidget` (控制按钮面板，点击发出信号)
    13. `include/TowerDefense/Gui/QtUIAdapter.hpp` 和 `src/Gui/QtUIAdapter.cpp`: 负责在所有对象创建后集中建立 Qt 信号槽连接。
    14. `apps/TowerDefenseGame/main.cpp`: 程序入口，初始化应用、`GameController`、GUI，连接适配器，运行事件循环。

### 阶段二: 地图I/O, 词缀系统逻辑与基础GUI (约1-1.5周)

* **逻辑**:
    * `Map`: 实现从文件 (如JSON) 导入/导出地图布局和路径。
    * `Affix` 抽象基类和具体词缀类 (文档要求的塔词缀和敌方词缀)。
    * `Unit`: 添加 `std::vector<std::unique_ptr<Affix>> affixes` 成员。
    * `Unit`: 实现 `addAffix`, `removeAffixByType`。
    * 词缀效果逻辑: 修改单位属性 (伤害、攻速、移速) 或行为 (冰冻、流血)。
    * 敌人掉落词缀的逻辑 (简化：击杀特定敌人获得特定词缀，存入一个全局词缀库)。
    * 塔可以安装/卸下词缀的逻辑 (通过 `GameController` 接口)。
* **GUI (Qt)**:
    * `MainWindow`: 添加"加载地图"菜单项，弹出 `QFileDialog`，选择地图文件传给 `GameController`。
    * `GameView`:
        * 改进单位渲染 (可以使用简单的占位符图片 `QPixmap`)。
        * 在单位头上绘制血条。
    * `InfoPanelWidget`: 当选中一个单位时，显示其详细属性 (包括已装备的词缀名称)。
    * (可选) `AffixInventoryDialog`: 一个简单的对话框，显示玩家拥有的词缀，并允许将词缀应用到选中的塔上 (如果塔有空槽)。
* **接口重点**:
    * `Unit` 类提供获取词缀信息的方法供GUI显示。
    * `GameController` 提供接口给GUI来操作词缀的安装/卸下。

### 阶段三: 攻击/受伤效果展示与GUI完善 (约1周)

* **逻辑**:
    * 远程塔: 实现发射飞行道具的逻辑 (`Projectile` 类，更新其位置)。
    * 飞行道具命中检测及伤害施加。
    * 近战塔: 攻击时可以有一个攻击计时器/动画状态。
* **GUI (Qt)**:
    * `GameView`:
        * 攻击效果:
            * 远程塔: 绘制飞行道具 (简单形状或图片)。
            * 近战塔: 塔播放一个简单的"攻击"动画 (如短暂改变颜色或形状)。
        * 受击效果: 单位闪烁，或者头上冒出伤害数字 (简单的 `QPainter::drawText`)。
        * 异常状态显示 (Extension 4.2):
            * 冰冻: 单位上覆盖一层淡蓝色半透明蒙版。
            * 流血: 单位头上周期性飘出红色小点或数字。
    * `ControlPanelWidget`: 添加 "暂停/继续" 按钮。
    * 完善 `InfoPanelWidget` 显示更多细节。
    * 状态栏 (`QStatusBar`): 显示游戏提示信息。
* **接口重点**:
    * `Unit` 类需要有状态来指示其是否正在攻击、受击，或者具有何种视觉特效。
    * `Projectile` 类的数据需要被 `GameView` 读取并渲染。

## 7. 策略：降低开发难度 & 取得高分

### 7.1 降低开发难度：

1.  **严格分阶段，迭代开发，细化周计划**。
2.  **KISS原则**。
3.  **逻辑与UI分离 (核心)**：通过UI抽象层实现。先完成无UI的核心逻辑。
4.  **组件化设计尝试**：对于单位和词缀系统，如果时间允许且希望有高扩展性，可以尝试引入组件化思想，即使是简化的版本。
5.  **善用C++特性**：STL, 智能指针, `const`正确性, `auto`, lambda, `enum class`, `override`, `final`。
6.  **配置文件驱动**：地图、单位属性、词缀效果、波数等使用JSON或类似格式。
7.  **日志系统**。
8.  **版本控制 (Git)**。
9.  **先功能后美化**。
10. **单元测试**：尽早引入，保证核心模块的正确性。

### 7.2 争取更高分数：

1.  **完成所有基础功能** (70%)。
2.  **有选择地完成选做内容 (Extensions)** (20%)。高性价比：资源系统, 远程攻击显示, 异常状态显示, 简单音效, **撤销/重做系统 (Command Pattern)**。
3.  **优秀的代码风格与工程规范** (10%)：
    * 遵循现代C++实践，清晰命名，适当注释，一致格式 (`clang-format`)。
    * 使用静态分析 (`clang-tidy`)，开启所有编译警告。
    * 规范的CMake项目结构和构建脚本。
    * 有效的单元测试。
4.  **健壮性**。
5.  **清晰的文档** (README, 代码注释)。
6.  **流畅的游戏体验**。

## 8. 现代C++代码风格建议 (同前，不再赘述，但需严格遵守)

* RAII, `nullptr`, 范围`for`, `auto`, Lambda, `enum class`, `override`/`final`, 移动语义, `const`正确性, 模块化接口。

## 9. 开发注意事项与质量保证

### 9.1 代码质量保证

1. **静态代码分析**：
   * 使用 `clang-tidy` 执行全面的静态代码分析，检查潜在的bug、性能问题和编码风格违反
   * 配置 `.clang-tidy` 文件，启用以下检查组：
     - `readability-*` - 代码可读性检查
     - `performance-*` - 性能优化检查
     - `modernize-*` - 现代C++特性使用检查
     - `bugprone-*` - 检测易出错的代码模式
     - `cppcoreguidelines-*` - C++核心指南规则检查

2. **代码格式化**：
   * 使用 `clang-format` 确保一致的代码格式
   * 配置 `.clang-format` 文件，遵循基于LLVM风格的自定义规则
   * 设置CI流程在提交代码前自动执行格式检查

3. **安全漏洞扫描**：
   * 定期运行安全扫描工具检测潜在的漏洞
   * 检查内存泄漏、缓冲区溢出等常见安全问题

### 9.2 现代C++实践

1. **内存管理**：
   * 严格遵循RAII原则，避免裸指针
   * 优先使用智能指针（`std::unique_ptr`、`std::shared_ptr`）管理动态资源
   * 避免显式使用`new`/`delete`，使用`std::make_unique`/`std::make_shared`
   * 使用STL容器代替手动内存管理的数组

2. **函数与参数传递**：
   * 使用引用或常量引用传递大型对象
   * 对于简单类型使用值传递
   * 尽可能使函数无副作用(pure functions)
   * 使用`std::optional`处理可能失败的操作，替代错误代码或异常

3. **类设计**：
   * 通过`final`、`override`增强继承安全性
   * 显式标记构造函数为`default`或`delete`
   * 遵循三/五规则（Rule of Three/Five）
   * 使用`=default`自动生成默认行为
   * 合理使用`explicit`关键字防止隐式类型转换

4. **现代语言特性**：
   * 使用`constexpr`进行编译期计算
   * 利用`auto`简化复杂类型声明
   * 应用结构化绑定简化返回多个值
   * 使用初始化列表统一初始化语法
   * 运用Lambda表达式简化回调和算法调用

### 9.3 性能优化策略

1. **设计阶段优化**：
   * 选择适当的数据结构（哈希表vs数组vs树）
   * 采用空间换时间的策略（预计算、缓存结果）
   * 设计良好的算法复杂度（尽可能避免O(n²)以上复杂度）

2. **实现阶段优化**：
   * 避免不必要的内存分配和复制
   * 使用引用避免对象拷贝
   * 移动语义(`std::move`)处理大型对象转移
   * 使用`noexcept`标记不抛出异常的函数

3. **游戏性能特定优化**：
   * 实现对象池(Object Pool)模式减少动态分配
   * 使用空间分区技术(Spatial Partitioning)优化碰撞检测
   * 对不在视野内的对象采用简化更新
   * 实现游戏逻辑与渲染分离，支持不同更新频率

### 9.4 测试策略

1. **单元测试**：
   * 使用GoogleTest或Catch2框架
   * 对核心算法和业务逻辑编写测试
   * 使用模拟对象(Mocks)隔离测试单元
   * 测试边界条件和异常情况

2. **集成测试**：
   * 测试模块之间的交互
   * 验证数据流动和状态转换

3. **性能测试**：
   * 对关键功能进行基准测试(benchmarking)
   * 监测CPU和内存使用情况
   * 确定性能瓶颈并优化

4. **手动测试**：
   * 玩家体验测试
   * 用户界面和交互测试
   * 异常操作测试

### 9.5 文档与注释

1. **代码注释**：
   * 为复杂算法和非直观逻辑添加注释
   * 使用Doxygen风格为公共API添加文档注释
   * 解释"为什么"而不仅仅是"是什么"
   * 标记TODO和FIXME项目

2. **技术文档**：
   * 维护高级架构文档
   * 记录设计决策和权衡
   * 提供API使用指南
   * 更新项目README和设置说明

## 10. 当前项目进度分析 (2024年12月更新)

### 10.1 项目实现状态总览

**整体完成度：约65%**

- **素材配置**: 95% 完成 ✅
- **核心逻辑**: 85% 完成 ✅
- **GUI组件**: 30% 完成 ⚠️
- **游戏集成**: 20% 完成 ❌

### 10.2 已完成模块详细分析

#### 10.2.1 素材系统 ✅ (已完成)
- **地图瓦片配置** (`tilesheet_simplified_config.json`):
  - 所有基础地形瓦片已配置 (路径、建造区、起点、终点)
  - UI数字和符号瓦片已配置
  - 瓦片坐标转换公式已定义
- **游戏数据配置** (`GameData.cpp`):
  - 塔类型数据完整 (近战塔、远程塔的统计和视觉数据)
  - 敌人类型数据完整 (包含词缀组合的贴图映射)
  - 平衡性数据已设置 (攻击力、血量、速度等)

#### 10.2.2 核心逻辑层 ✅ (基本完成)
- **GameController** (436行): 完整的游戏控制器，包含状态管理和事件系统
- **UnitManager** (257行): 单位管理系统，支持创建、更新、销毁
- **Map** (509行): 地图系统，支持路径查找和瓦片管理
- **Tower/Enemy** (261/191行): 单位类实现，包含基础AI和属性系统
- **WaveManager** (162行): 波次管理系统
- **ResourceManager** (23行): 资源管理基础框架

#### 10.2.3 工厂和数据系统 ✅ (已完成)
- **UnitFactory** (81行): 工厂模式实现，支持动态创建单位
- **GameData**: 单例模式的游戏数据管理
- **Types.hpp**: 完整的类型定义和枚举

### 10.3 关键缺失组件分析

#### 10.3.1 GUI渲染系统 ❌ (缺失 - 阻塞游戏运行)
- **GameViewWidget**: 核心游戏视图组件，负责:
  - 地图瓦片渲染
  - 单位位置和状态显示
  - 鼠标交互处理 (点击放塔)
  - 游戏动画更新
- **重要性**: 没有此组件游戏无法可视化运行

#### 10.3.2 UI控制面板 ❌ (缺失 - 影响用户体验)
- **InfoPanelWidget**: 信息显示面板 (金钱、生命值、波次)
- **TowerPaletteWidget**: 塔选择面板
- **ControlPanelWidget**: 游戏控制面板 (开始、暂停、速度)
- **重要性**: 没有这些组件用户无法控制游戏

#### 10.3.3 信号槽集成 ❌ (缺失 - 影响逻辑UI交互)
- **QtUIAdapter**: Qt信号槽连接适配器
- **MainWindow集成**: 当前MainWindow槽函数为空实现
- **重要性**: 没有此系统游戏逻辑与UI无法通信

### 10.4 急需解决的技术债务

1. **MainWindow.cpp**: 槽函数均为空实现，需要实际功能
2. **缺少游戏循环**: 没有定时器驱动的游戏更新循环
3. **渲染系统空白**: 没有实际的绘制和显示代码
4. **用户输入处理**: 鼠标键盘事件处理未实现

## 11. 下一步开发计划 (确保可运行游戏)

### 11.1 第一优先级: 基础GUI实现 (预计1-2天)

**目标**: 实现最基础的可视化游戏，能看到地图和单位

#### 任务1.1: 实现GameViewWidget
```cpp
// 需要实现的核心功能:
class GameViewWidget : public QWidget {
    Q_OBJECT
public:
    void paintEvent(QPaintEvent* event) override;  // 渲染地图和单位
    void mousePressEvent(QMouseEvent* event) override;  // 处理点击
    void timerEvent(QTimerEvent* event) override;  // 游戏循环更新

    // 渲染方法
    void drawMap(QPainter& painter);
    void drawUnits(QPainter& painter);
    void drawUI(QPainter& painter);

    // 坐标转换
    QPoint screenToGrid(QPoint screenPos);
    QPoint gridToScreen(QPoint gridPos);
};
```

#### 任务1.2: 实现基础UI面板
- **InfoPanelWidget**: 显示资源和状态的简单文本控件
- **TowerPaletteWidget**: 2个按钮选择塔类型
- **ControlPanelWidget**: 开始/暂停按钮

#### 任务1.3: 集成到MainWindow
- 创建布局管理器组织各组件
- 建立基础的信号槽连接
- 实现游戏循环定时器

**完成标准**:
- 可以看到地图渲染
- 可以看到单位位置
- 点击可以放置塔 (基础功能)

### 11.2 第二优先级: 游戏循环和交互 (预计1-2天)

**目标**: 实现完整的游戏流程，从开始到结束

#### 任务2.1: 完善GameController集成
- 在主窗口中初始化GameController
- 建立定时器驱动的update循环
- 实现游戏状态切换 (菜单->游戏->结束)

#### 任务2.2: 实现QtUIAdapter
- 连接GameController信号到UI槽函数
- 实现UI事件到GameController的转发
- 确保数据双向同步

#### 任务2.3: 完善用户交互
- 鼠标点击放置塔
- 塔类型选择
- 游戏控制 (开始波次、暂停等)

**完成标准**:
- 可以完整游玩一轮游戏
- 敌人会沿路径移动
- 塔会自动攻击敌人
- 有基本的胜负判定

### 11.3 第三优先级: 功能完善和优化 (预计2-3天)

**目标**: 完善游戏机制，提升用户体验

#### 任务3.1: 完善战斗系统
- 实现攻击动画和效果
- 添加词缀系统的视觉反馈
- 优化AI行为

#### 任务3.2: 完善UI和反馈
- 添加攻击特效显示
- 实现伤害数字
- 优化界面布局和美化

#### 任务3.3: 添加扩展功能
- 实现部分Extension功能 (如投射物显示)
- 添加音效支持
- 实现存档/读档

**完成标准**:
- 游戏体验流畅完整
- 视觉效果丰富
- 支持核心玩法功能

### 11.4 实现顺序建议

**推荐的文件创建/修改顺序**:

1. `include/TowerDefense/Gui/GameViewWidget.hpp` + `src/Gui/GameViewWidget.cpp`
2. `include/TowerDefense/Gui/InfoPanelWidget.hpp` + `src/Gui/InfoPanelWidget.cpp`
3. `include/TowerDefense/Gui/TowerPaletteWidget.hpp` + `src/Gui/TowerPaletteWidget.cpp`
4. `include/TowerDefense/Gui/ControlPanelWidget.hpp` + `src/Gui/ControlPanelWidget.cpp`
5. 修改 `src/Gui/MainWindow.cpp` - 集成所有组件
6. 修改 `src/Gui/forms/MainWindowForm.ui` - 设计UI布局
7. `include/TowerDefense/Gui/QtUIAdapter.hpp` + `src/Gui/QtUIAdapter.cpp`
8. 修改 `apps/TowerDefenseGame/main.cpp` - 完善初始化流程

### 11.5 风险预警

**潜在阻塞点**:
1. **Qt渲染性能**: 大量单位时可能需要优化绘制
2. **坐标系统**: 屏幕坐标与游戏逻辑坐标转换
3. **内存管理**: Qt对象生命周期管理
4. **信号槽复杂度**: 避免循环依赖和信号风暴

**缓解策略**:
1. 先实现基础功能，后优化性能
2. 建立清晰的坐标转换工具函数
3. 使用智能指针和RAII原则
4. 设计清晰的信号槽架构

### 11.6 成功标准

**最小可行游戏 (MVP) 标准**:
- [ ] 可以启动并看到游戏界面
- [ ] 可以在地图上放置塔
- [ ] 敌人会自动生成并沿路径移动
- [ ] 塔会自动攻击范围内的敌人
- [ ] 敌人死亡后玩家获得金钱
- [ ] 敌人到达终点后玩家失去生命
- [ ] 有基本的胜利/失败条件

**完整游戏标准**:
- [ ] MVP功能全部实现
- [ ] 词缀系统可视化
- [ ] 攻击效果和动画
- [ ] 完善的UI控制
- [ ] 游戏平衡性调试
- [ ] 至少支持一种Extension功能
