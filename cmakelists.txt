cmake_minimum_required(VERSION 3.10)

project(TowerDefense VERSION 1.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    add_compile_options(-Wall -Wextra -Wpedantic)
    add_compile_options(-Werror)
endif()

# --- 查找和链接库 ---
find_package(Qt6 COMPONENTS Core Gui Widgets REQUIRED)

if(POLICY CMP0167)
    cmake_policy(SET CMP0167 OLD)
endif()

find_package(Boost REQUIRED)
set(BOOST_ROOT "/opt/homebrew/Cellar/boost/1.88.0")
include_directories(${Boost_INCLUDE_DIRS})

# --- 添加子目录 ---
add_subdirectory(src/Core)
add_subdirectory(src/GameLogic)
add_subdirectory(src/Utils)
add_subdirectory(src/Gui)
add_subdirectory(apps/TowerDefenseGame)