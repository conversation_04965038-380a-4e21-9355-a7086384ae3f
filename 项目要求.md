# **塔防游戏功能列表**

本文档详细列出了塔防游戏项目需要实现的各项功能，按照项目阶段进行划分。

## **第一阶段：基础框架与核心机制**

### **1\. 阵营设定**

* **我方阵营**：由玩家操控。
* **敌方阵营**：由程序操控。
* **胜利条件**：
  * 我方：在有限次攻击中阻止所有敌方单位穿过地图。
  * 敌方：在有限次攻击内成功让单位穿过地图。

### **2\. 地图系统**

* **地图结构**：
  * 逻辑上为一个 m x n 的矩形网格。
  * 每个 1x1 的矩形为一个“格子”。
* **路径设定**：
  * 地图上存在若干条**有向路径**。
  * 路径是格子的一个有穷序列，序列中相邻格子需**四邻接**（上下左右），不允许斜向连接。
  * 路径中**不存在重复的格子**。
  * 路径有明确的**起点格子**和**终点格子**。
  * 敌方单位从路径起点出现，沿路径序列移动至终点。
  * **不同路径可以共用格子**，包括起点和终点。
* **地图接口**：预留接口，方便后续阶段扩展地图数量和样式。

### **3\. 单位系统**

* **通用属性**：
  * 每个单位拥有一定的**生命值**，生命值过低则单位被破坏。
* **我方单位（塔）**：
  * **部署机制**：
    * 玩家可以在地图上的**合适位置**部署单位。
    * 提供界面供玩家选择部署位置和单位类型。
  * **近战塔**：
    * **部署位置**：路径上。
    * **攻击范围**：1（仅攻击相邻格子的敌人）。
    * **特性**：能**阻拦**敌方单位前进。
    * **伤害方式**：一次只能攻击一个敌人，无范围伤害。
  * **远程塔**：
    * **部署位置**：路径之外，且与路径相邻的格子上。
    * **攻击范围**：自行设定默认攻击范围。
    * **伤害方式**：一次只能攻击一个敌人，无范围伤害。
* **敌方单位**：
  * **控制方式**：不受我方控制，由程序控制。
  * **生成机制**：在路径的**起始点随机生成**。
  * **移动策略**：按照一定策略向路径的结束点移动。
  * **攻击行为**：攻击沿途处在攻击范围内的我方单位。
  * **默认近战敌人**：阶段一仅需实现此类型敌人，但需预留接口。

### **4\. 核心战斗逻辑**

* **基础属性**：为单位设定血量、伤害值。
* **对战过程**：实现基础的敌我双方对战逻辑。
* **显示方式**：
  * 可以通过控制台日志显示战斗过程。
  * （可选）初步的图形界面显示。

### **5\. \[拓展功能 2.1\] 资源系统**

* **资源消耗**：部署单位需要消耗资源。
* **资源增长**：资源随时间流逝自动增加。
* **单位撤退**：我方可以手动撤退已部署的单位，可设定是否返还资源。
* **特殊单位**：可设定能够产出资源的特殊单位。

## **第二阶段：地图编辑与词缀系统**

### **1\. 地图的导入与导出**

* **功能要求**：游戏支持地图的导入和导出功能。
* **格式设计**：自行设计一套文本文件格式来表达地图信息（如尺寸、路径、起点终点位置等）。
* **用户流程**：
  1. 导出默认地图。
  2. 用户参考格式修改或创建新地图文件。
  3. 将新地图导入游戏并能正常游玩。

### **2\. 词缀系统**

* **我方塔词缀**：
  * **获取方式**：杀死敌方单位掉落。
  * **词缀库**：拾取的词缀保存在词缀库中。
  * **安装/卸下**：玩家可以打开词缀库，为我方塔安装或卸下词缀。
  * **词缀槽**：近战塔和远程塔均有两个词缀槽，一个槽安装一类词缀。
  * **效果**：安装后塔的属性（伤害、攻击范围、攻击间隔、其他机制）发生改变。
  * **近战塔基础词缀**：
    * **狂暴的**：附加狂暴属性，装备后伤害和攻击间隔都大幅增加（例如乘以2）。
    * **冰系的**：附加冰冻效果，攻击能使敌方单位停止攻击、移动一段时间。
    * **群伤的**：附加范围伤害，能同时攻击多个敌人。
  * **远程塔基础词缀**：
    * **放血的**：攻击附加流血效果，使敌人在一定时间内持续扣血。
* **敌方单位词缀**：
  * **词缀数量**：一个敌方单位最多拥有不超过两类词缀（可以为空）。
  * **组合方式**：词缀可以自由组合。
  * **基础敌方词缀**：
    * **闪现的**：单位能发动闪现，越过我方近战塔的阻挡前进（有冷却时间）。
    * **神速的**：单位移动速度超过默认单位。
* **词缀显示** (若有GUI)：
  * 对不同词缀的单位进行区分，例如在单位头上用文字标出词缀。

### **3\. \[拓展功能 3.1\] 更多词缀**

* **我方词缀示例**：自带回血效果、削减敌人伤害。
* **敌方词缀示例**：为自己增加攻击速度。
* **注意**：避免词缀机制互相冲突。

## **第三阶段：图形界面与效果展示**

### **1\. 图形用户界面 (GUI)**

* **强制要求**：必须实现图形界面（若前两阶段未完成）。
  * 可以使用如 Qt、ncurses 等库。
* **功能展示**：
  * 清晰显示前面阶段实现的各种单位的部署。
  * 清晰显示单位的移动过程。

### **2\. 单位攻击与受伤效果展示**

* **视觉反馈**：在游戏界面中展示双方单位的攻击、受伤和异常效果。
* **实现方式举例**：
  * 单位图形闪烁。
  * 在单位头上飘一个数字表示受到的伤害值。
  * 在单位头上绘制血条显示。
  * （欢迎其他创意实现，只要有动态效果即可）。

### **3\. \[拓展功能 4.1\] 远程攻击显示**

* **目标指示**：对于远程单位，需要表示出它在攻击哪个或哪些敌方单位。
* **实现方式举例**：
  * 发射飞向敌方单位的飞行物（攻击非立即生效）。
  * 绘制一道光束（攻击立即生效）。

### **4\. \[拓展功能 4.2\] 异常状态显示**

* **视觉体现**：对受到的某些异常状态（如流血、冰冻）在画面上进行体现。
* **实现方式举例**：
  * 头顶飘字。
  * 在敌方单位图像上添加特定颜色或效果。

## **第四阶段：扩展与创新**

此阶段为扩展阶段，鼓励添加更多有趣和创新的功能。

### **1\. 完成前期拓展功能**

* 实现前三阶段中标记为 "Extension" 但尚未完成的功能。

### **2\. 自由创新**

* **音效**：为游戏添加适当的音效。
* **动画**：设计酷炫的动画效果以提升用户体验。
* **其他任何有趣的想法**。

## **通用要求（贯穿所有阶段）**

* **编程语言与范式**：使用 C++ 语言，遵循面向对象和泛型程序设计范式。
* **原创性**：所有代码必须由自己完成，严禁抄袭。
* **图形库**：推荐使用第三方的 C++ 图形库来完成图形绘制、事件处理等。
* **代码风格**：保持良好的代码风格，占总分10%。
* **阶段性交付**：项目按阶段划分，每个阶段有明确的验收内容。