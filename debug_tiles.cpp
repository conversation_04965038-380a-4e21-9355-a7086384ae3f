#include <iostream>
#include <fstream>
#include <string>
#include <vector>

int main() {
    std::ifstream file("assets/data/maps/ComplexSnakeMap.map");
    std::string line;
    bool inTilesSection = false;
    int row = 0;
    
    std::cout << "Tile layout with coordinates:" << std::endl;
    std::cout << "   ";
    for (int col = 0; col < 20; col++) {
        std::cout << col % 10;
    }
    std::cout << std::endl;
    
    while (std::getline(file, line)) {
        if (line == "[Tiles]") {
            inTilesSection = true;
            continue;
        }
        if (line.empty() || line[0] == '#') continue;
        if (line[0] == '[') {
            inTilesSection = false;
            continue;
        }
        
        if (inTilesSection && !line.empty()) {
            std::cout << row << ": " << line << std::endl;
            
            // Print path tiles
            for (int col = 0; col < line.length(); col++) {
                if (line[col] == 'S' || line[col] == 'P' || line[col] == 'E') {
                    std::cout << "  Path tile '" << line[col] << "' at (" << col << "," << row << ")" << std::endl;
                }
            }
            row++;
        }
    }
    
    return 0;
}
