#include "include/TowerDefense/GameLogic/Map.hpp"
#include "include/TowerDefense/Types.hpp"
#include <QCoreApplication>
#include <QDebug>
#include <iostream>
#include <set>

void testMapValidation(const std::string& mapPath, const std::string& mapName) {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "Testing: " << mapName << std::endl;
    std::cout << "Path: " << mapPath << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    
    Map testMap;
    bool success = testMap.loadFromFile(mapPath);
    
    if (!success) {
        std::cout << "✗ FAILED to load map!" << std::endl;
        return;
    }
    
    std::cout << "✓ Map loaded successfully!" << std::endl;
    
    // Basic properties
    std::cout << "\nBasic Properties:" << std::endl;
    std::cout << "  Dimensions: " << testMap.getWidth() << "x" << testMap.getHeight() << std::endl;
    std::cout << "  Tile size: " << testMap.getTileSize() << std::endl;
    std::cout << "  Number of paths: " << testMap.getPathCount() << std::endl;
    
    // Count different tile types
    int startPoints = 0, endPoints = 0, pathTiles = 0, buildableGround = 0, obstacles = 0;
    
    for (int y = 0; y < testMap.getHeight(); y++) {
        for (int x = 0; x < testMap.getWidth(); x++) {
            TileType type = testMap.getTileType(x, y);
            switch (type) {
                case TileType::START_POINT: startPoints++; break;
                case TileType::END_POINT: endPoints++; break;
                case TileType::PATH: pathTiles++; break;
                case TileType::BUILDABLE_GROUND: buildableGround++; break;
                case TileType::OBSTACLE: obstacles++; break;
                default: break;
            }
        }
    }
    
    std::cout << "\nTile Type Counts:" << std::endl;
    std::cout << "  Start points: " << startPoints << std::endl;
    std::cout << "  End points: " << endPoints << std::endl;
    std::cout << "  Path tiles: " << pathTiles << std::endl;
    std::cout << "  Buildable ground: " << buildableGround << std::endl;
    std::cout << "  Obstacles: " << obstacles << std::endl;
    
    // Validate path connectivity
    if (testMap.getPathCount() > 0) {
        std::cout << "\nPath Analysis:" << std::endl;
        for (size_t i = 0; i < testMap.getPathCount(); i++) {
            auto const& path = testMap.getPath(i);
            std::cout << "  Path " << i << ": " << path.size() << " waypoints" << std::endl;
            
            if (!path.empty()) {
                auto start = path.front();
                auto end = path.back();
                std::cout << "    Start: (" << start.x() << ", " << start.y() << ")" << std::endl;
                std::cout << "    End: (" << end.x() << ", " << end.y() << ")" << std::endl;
                
                // Verify start and end tiles
                TileType startType = testMap.getTileType(static_cast<int>(start.x()), static_cast<int>(start.y()));
                TileType endType = testMap.getTileType(static_cast<int>(end.x()), static_cast<int>(end.y()));
                
                std::cout << "    Start tile type: " << static_cast<int>(startType) 
                         << (startType == TileType::START_POINT ? " ✓" : " ✗") << std::endl;
                std::cout << "    End tile type: " << static_cast<int>(endType) 
                         << (endType == TileType::END_POINT ? " ✓" : " ✗") << std::endl;
            }
        }
    }
    
    // Test buildability
    std::cout << "\nBuildability Tests:" << std::endl;
    int meleeBuildable = 0, rangedBuildable = 0;
    
    for (int y = 0; y < testMap.getHeight(); y++) {
        for (int x = 0; x < testMap.getWidth(); x++) {
            if (testMap.isBuildable(x, y, TowerType::MELEE)) {
                meleeBuildable++;
            }
            if (testMap.isBuildable(x, y, TowerType::RANGED)) {
                rangedBuildable++;
            }
        }
    }
    
    std::cout << "  Melee tower buildable positions: " << meleeBuildable << std::endl;
    std::cout << "  Ranged tower buildable positions: " << rangedBuildable << std::endl;
    
    // Test coordinate conversion
    std::cout << "\nCoordinate Conversion Tests:" << std::endl;
    QPointF worldPos = testMap.gridToWorld(5, 5);
    QPoint gridPos = testMap.worldToGrid(worldPos);
    std::cout << "  Grid (5,5) -> World (" << worldPos.x() << "," << worldPos.y() 
              << ") -> Grid (" << gridPos.x() << "," << gridPos.y() << ")" << std::endl;
    
    bool conversionOk = (gridPos.x() == 5 && gridPos.y() == 5);
    std::cout << "  Coordinate conversion: " << (conversionOk ? "✓" : "✗") << std::endl;
    
    std::cout << "\n" << mapName << " validation completed!" << std::endl;
}

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    std::cout << "Comprehensive Map Testing Suite" << std::endl;
    std::cout << "===============================" << std::endl;
    
    // Test both maps
    testMapValidation("assets/data/maps/ComplexSnakeMap.map", "ComplexSnakeMap");
    testMapValidation("assets/data/maps/StandardMap.map", "StandardMap");
    
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "All tests completed!" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    
    return 0;
}
