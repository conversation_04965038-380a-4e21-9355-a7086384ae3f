add_executable(TowerDefenseGame
    main.cpp
)

target_link_libraries(TowerDefenseGame
    PUBLIC
    Core
    GameLogic
    Utils
    Gui

    Qt6::Widgets
    Qt6::Gui
)

# 复制 assets 目录到可执行文件目录
# 获取可执行文件的输出目录
get_target_property(OUTPUT_DIR TowerDefenseGame RUNTIME_OUTPUT_DIRECTORY)

# 如果 OUTPUT_DIR 为空，使用默认的位置
if(NOT OUTPUT_DIR)
    # 对于多配置生成器 (Xcode, Visual Studio)
    if(CMAKE_CONFIGURATION_TYPES)
        set(OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}/$<CONFIG>)
    else()
        # 对于单配置生成器 (Makefi<PERSON>, Ninja)
        set(OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR})
    endif()
endif()

# 定义源 assets 目录和目标 assets 目录
set(ASSETS_SOURCE_DIR ${CMAKE_SOURCE_DIR}/assets)
set(ASSETS_DESTINATION_DIR ${OUTPUT_DIR}/assets) # 复制到可执行文件目录下的 assets 子目录

# 添加一个自定义命令来复制目录
add_custom_command(
    TARGET TowerDefenseGame POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${ASSETS_SOURCE_DIR}
    ${ASSETS_DESTINATION_DIR}
    COMMENT "Copying game assets to the executable directory: ${ASSETS_DESTINATION_DIR}"
)

# 为了调试，添加一个输出信息
message(STATUS "TowerDefenseGame: Assets will be copied to: ${ASSETS_DESTINATION_DIR}")

# 同时，确保在运行时也能找到assets，添加一个额外的复制到项目根目录的build目录
add_custom_command(
    TARGET TowerDefenseGame POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${ASSETS_SOURCE_DIR}
    ${CMAKE_BINARY_DIR}/assets
    COMMENT "Copying game assets to build root directory as backup"
)