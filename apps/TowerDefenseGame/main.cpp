#include "TowerDefense/Core/GameController.hpp"
#include "TowerDefense/GameLogic/Map.hpp"
#include "TowerDefense/Gui/MainWindow.hpp"
#include "TowerDefense/Utils/TileRenderer.hpp"
#include "TowerDefense/Utils/TilesheetConfig.hpp"

#include <QApplication>
#include <QDebug>

int main(int argc, char* argv[]) {
    QApplication a(argc, argv);

    qDebug() << "Starting Tower Defense Game...";

    // 初始化资产系统
    auto tilesheetConfig = std::make_shared<TilesheetConfig>("assets/Tilesheet/towerDefense_tilesheet.png",
                                                             "assets/data/tilesheet_simplified_config.json");

    if (!tilesheetConfig->isLoaded()) {
        qWarning() << "Failed to load tilesheet configuration!";
        return -1;
    }

    auto tileRenderer = std::make_shared<TileRenderer>(tilesheetConfig);
    qDebug() << "Asset system initialized successfully";

    // 确保游戏控制器单例被初始化并启动游戏
    GameController& gameController = GameController::getInstance();

    // 直接使用 GameController 来加载并启动关卡，这将内部创建 Map、UnitManager、WaveManager 并把状态设为 PLAYING
    std::string const mapFile = "assets/data/maps/StandardMap.map";
    gameController.startGame(mapFile);

    // 从 GameController 获取当前地图，以便交给 GUI 渲染
    std::shared_ptr<Map> map = gameController.getCurrentMap();

    if (!map) {
        qWarning() << "Failed to obtain map from GameController!";
        return -1;
    }

    qDebug() << "GameController initialized successfully";

    // 创建主窗口
    MainWindow w;

    // 设置渲染系统到主窗口
    w.setTileRenderer(tileRenderer);
    w.setMap(map);
    qDebug() << "Asset system integrated with MainWindow";

    w.show();

    qDebug() << "Application started successfully";

    return a.exec();
}
