#include "TowerDefense/GameLogic/Enemy.hpp"

#include "TowerDefense/Core/GameData.hpp"
#include "TowerDefense/GameLogic/Map.hpp"
#include "TowerDefense/GameLogic/Tower.hpp"
#include "TowerDefense/GameLogic/Unit.hpp"
#include "TowerDefense/Types.hpp"

#include <QDebug>
#include <cmath>

#ifndef M_PI
#    define M_PI 3.14159265358979323846
#endif

// Enemy类实现

// 构造函数
Enemy::Enemy(UnitID id,
             EnemyType enemyType,
             UnitType unitType,
             Map const& map,
             int pathIndex,                                     // 敌人在地图上的路径索引
             int initialHealth,                                 // 敌人的初始生命值
             float moveSpeed,                                   // 敌人的移动速度
             int killReward)                                    // 击败敌人的金币奖励
    : Unit(id, unitType, QPointF(), initialHealth, moveSpeed),  // 位置会在updateMovement中设置
      m_enemyType(enemyType),
      m_map(map),
      m_pathIndex(pathIndex),
      m_rewardCoins(killReward) {
    // 确保路径索引有效
    if (pathIndex >= 0 && static_cast<size_t>(pathIndex) < map.getPathCount()) {
        // 设置初始位置为路径的起点
        auto const& path = map.getPath(pathIndex);
        if (!path.empty()) {
            // Convert grid waypoint to world coordinates (tile center)
            QPointF wp0 = m_map.gridToWorld(static_cast<int>(path.front().x()), static_cast<int>(path.front().y()));
            m_position  = wp0;
            qDebug() << "Created enemy with ID:" << id << "at start position (world):" << m_position;
        }
    } else {
        qDebug() << "Warning: Enemy created with invalid path index:" << pathIndex;
    }
}

void Enemy::update(float deltaTime) {
    // 如果已经死亡或逃脱，不需要更新
    if (isDead() || hasEscaped()) {
        return;
    }

    // 更新敌人在路径上的位置
    updateMovement(deltaTime);

    // 更新组件
    for (auto const& component : m_components) {
        component->update(deltaTime);
    }
}

bool Enemy::isDead() const {
    return m_health <= 0;
}

bool Enemy::hasEscaped() const {
    return m_escaped;
}

bool Enemy::addAffix(std::shared_ptr<Affix> const& affix) {
    if (m_affixes.size() >= MAX_AFFIXES) {
        return false;  // 无法添加更多词缀
    }

    m_affixes.push_back(affix);
    return true;
}

bool Enemy::removeAffix(unsigned int affixIndex) {
    if (affixIndex >= m_affixes.size()) {
        return false;  // 索引无效
    }

    m_affixes.erase(m_affixes.begin() + affixIndex);
    return true;
}

std::vector<std::shared_ptr<Affix>> const& Enemy::getAffixes() const {
    return m_affixes;
}

void Enemy::updateMovement(float deltaTime) {
    // 获取当前路径
    auto const& path = m_map.getPath(m_pathIndex);
    if (path.empty()) {
        return;  // 路径为空，无法移动
    }

    // 如果已经到达终点
    if (static_cast<size_t>(m_waypointIndex) >= path.size() - 1 && m_pathProgress >= 1.0F) {
        m_escaped = true;
        qDebug() << "Enemy" << m_id << "escaped!";
        return;
    }

    // Helper lambda to get waypoint in world coordinates
    auto waypointWorld = [this](QPointF const& gridPt) {
        return m_map.gridToWorld(static_cast<int>(gridPt.x()), static_cast<int>(gridPt.y()));
    };

    // 更新路径进度
    float distanceToMove = m_speed * deltaTime;

    // 计算当前位置到下一个航点的距离并更新朝向
    QPointF currentPos   = m_position;
    QPointF nextWaypoint = waypointWorld(path[m_waypointIndex + 1]);
    QPointF direction    = nextWaypoint - currentPos;
    auto distanceToWaypoint
        = static_cast<float>(std::sqrt((direction.x() * direction.x()) + (direction.y() * direction.y())));

    if (distanceToWaypoint > 0.0F) {
        // 记录朝向角度（度），0度指向右侧
        m_rotation = static_cast<float>(std::atan2(direction.y(), direction.x()) * 180.0 / M_PI);
    }

    // 如果下一航点所在的格子被近战塔阻挡，则停止移动
    if (static_cast<size_t>(m_waypointIndex) < path.size() - 1) {
        int nextGridX = static_cast<int>(path[m_waypointIndex + 1].x());
        int nextGridY = static_cast<int>(path[m_waypointIndex + 1].y());
        if (m_map.isBlocked(nextGridX, nextGridY)) {
            // 被阻挡，暂时无法前进
            return;
        }
    }

    // 通过循环而非递归处理移动
    while (distanceToMove > 0.0F && !m_escaped) {
        if (distanceToMove >= distanceToWaypoint) {
            // 可以到达下一个航点
            m_position = nextWaypoint;
            m_waypointIndex++;
            distanceToMove -= distanceToWaypoint;

            // 更新路径总进度
            if (static_cast<size_t>(m_waypointIndex) < path.size() - 1) {
                m_pathProgress = static_cast<float>(m_waypointIndex) / static_cast<float>(path.size() - 1);

                // 准备下一段移动
                currentPos   = m_position;
                nextWaypoint = waypointWorld(path[m_waypointIndex + 1]);
                direction    = nextWaypoint - currentPos;
                distanceToWaypoint
                    = static_cast<float>(std::sqrt((direction.x() * direction.x()) + (direction.y() * direction.y())));
            } else {
                // 到达终点
                m_pathProgress = 1.0F;
                m_escaped      = true;
                qDebug() << "Enemy" << m_id << "escaped!";
            }
        } else {
            // 在航点之间移动
            if (distanceToWaypoint > 0.0F) {
                direction  /= distanceToWaypoint;  // 归一化
                m_position += direction * distanceToMove;

                // 更新路径进度
                float segmentProgress  = distanceToMove / distanceToWaypoint;
                float segmentWeight    = 1.0F / static_cast<float>(path.size() - 1);
                m_pathProgress        += segmentProgress * segmentWeight;

                // 更新朝向角度
                m_rotation = static_cast<float>(std::atan2(direction.y(), direction.x()) * 180.0 / M_PI);
            }
            distanceToMove = 0.0F;  // 确保退出循环
        }
    }
}

// NormalEnemy类实现

NormalEnemy::NormalEnemy(UnitID id, Map const& map, int pathIndex)
    : Enemy(id, EnemyType::MELEE, UnitType::ENEMY_MELEE, map, pathIndex, 100, 1.0F, 10) {
    try {
        // 从 GameData 获取敌人的属性
        auto const& stats = GameData::getInstance().getStatsForEnemy(EnemyType::MELEE);

        // 使用 GameData 中的数据设置敌人的属性
        setHealth(stats.maxHealth);
        m_speed       = stats.speed;
        m_rewardCoins = stats.killReward;

        qDebug() << "NormalEnemy created with ID:" << id << "| HP:" << stats.maxHealth << "| Speed:" << stats.speed
                 << "| Reward:" << stats.killReward << "| Penalty:" << stats.escapePenalty;
    } catch (std::exception const& e) {
        qWarning() << "Failed to get NormalEnemy stats, using default values:" << e.what();
    }
}

int NormalEnemy::getRewardCoins() const {
    try {
        return GameData::getInstance().getStatsForEnemy(EnemyType::MELEE).killReward;
    } catch (std::exception const&) {
        return m_rewardCoins;  // 使用默认值作为备用
    }
}

int NormalEnemy::getEscapePenalty() const {
    try {
        return GameData::getInstance().getStatsForEnemy(EnemyType::MELEE).escapePenalty;
    } catch (std::exception const&) {
        return 1;  // 默认逃脱惩罚值
    }
}

// FastEnemy类实现

// ArmoredEnemy类实现

// 其他敌人类型实现已简化，只保留基础的NormalEnemy
// FastEnemy和ArmoredEnemy的功能通过词缀系统实现