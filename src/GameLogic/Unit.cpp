#include "TowerDefense/GameLogic/Unit.hpp"

#include "TowerDefense/GameLogic/UnitComponent.hpp"
#include "TowerDefense/Types.hpp"

Unit::Unit(UnitID id, UnitType type, QPointF const& position, int health, float speed)
    : m_position(position), m_health(health), m_speed(speed), m_type(type), m_id(id) {
}

// 添加组件
void Unit::addComponent(std::unique_ptr<UnitComponent> component) {
    if (component) {
        component->initialize(this);
        m_components.push_back(std::move(component));
    }
}

// 获取组件
UnitComponent* Unit::getComponent(ComponentType type) const {
    for (auto const& component : m_components) {
        if (component->getType() == type) {
            return component.get();
        }
    }
    return nullptr;
}

// Note: getComponentAs is a template and its implementation is in the header