#include "TowerDefense/GameLogic/WaveManager.hpp"

#include "TowerDefense/GameLogic/Map.hpp"
#include "TowerDefense/GameLogic/UnitManager.hpp"

#include <QDebug>

// 构造函数
WaveManager::WaveManager(Map const& map, UnitManager& unitManager)
    : m_map(map),
      m_unitManager(unitManager),
      m_currentWaveIndex(-1),
      m_waveInProgress(false),
      m_timeSinceLastSpawn(0.0F),
      m_enemiesSpawned(0) {
    qDebug() << "WaveManager created";

    // 加载波次数据
    loadWaveData();

    // 使用 m_map 以避免未使用警告（例如日志输出路径数量）
    qDebug() << "WaveManager linked to map with" << m_map.getPathCount() << "paths";
}

// 更新函数
void WaveManager::update(float deltaTime) {
    if (!m_waveInProgress) {
        return;
    }

    // 确保当前波次索引有效
    if (m_currentWaveIndex < 0 || static_cast<size_t>(m_currentWaveIndex) >= m_waves.size()) {
        m_waveInProgress = false;
        return;
    }

    // 获取当前波次数据
    WaveData const& currentWave = m_waves[m_currentWaveIndex];

    // 增加生成计时器
    m_timeSinceLastSpawn += deltaTime;

    // 检查是否应该生成新敌人
    if (m_timeSinceLastSpawn >= currentWave.spawnInterval && m_enemiesSpawned < currentWave.enemies.size()) {
        // 生成下一个敌人
        EnemyType type = currentWave.enemies[m_enemiesSpawned];
        qDebug() << "*** SPAWNING ENEMY ***";
        qDebug() << "Enemy type:" << static_cast<int>(type);
        qDebug() << "Path index:" << currentWave.pathIndex;
        qDebug() << "Enemy" << (m_enemiesSpawned + 1) << "of" << currentWave.enemies.size();

        spawnEnemy(type, currentWave.pathIndex);

        // 重置计时器，增加已生成计数
        m_timeSinceLastSpawn = 0.0F;
        m_enemiesSpawned++;

        qDebug() << "Spawned enemy" << m_enemiesSpawned << "of" << currentWave.enemies.size() << "in wave"
                 << (m_currentWaveIndex + 1);
    } else if (m_enemiesSpawned < currentWave.enemies.size()) {
        // Debug: Show why we're not spawning yet
        qDebug() << "Not spawning yet - Time since last spawn:" << m_timeSinceLastSpawn
                 << "Spawn interval:" << currentWave.spawnInterval << "Enemies spawned:" << m_enemiesSpawned << "/"
                 << currentWave.enemies.size();
    }

    // 检查波次是否完成
    if (m_enemiesSpawned >= currentWave.enemies.size()) {
        m_waveInProgress = false;
        qDebug() << "Wave" << (m_currentWaveIndex + 1) << "completed";
    }
}

// 开始下一波敌人
void WaveManager::startNextWave() {
    qDebug() << "=== WAVE MANAGER START NEXT WAVE ===";
    qDebug() << "Current wave index:" << m_currentWaveIndex;
    qDebug() << "Wave in progress:" << m_waveInProgress;
    qDebug() << "Total waves loaded:" << m_waves.size();

    // 如果当前有波次在进行，不做任何操作
    if (m_waveInProgress) {
        qDebug() << "Cannot start next wave - current wave still in progress";
        return;
    }

    // 移动到下一波
    m_currentWaveIndex++;
    qDebug() << "Incremented wave index to:" << m_currentWaveIndex;

    // 检查是否还有波次
    if (static_cast<size_t>(m_currentWaveIndex) >= m_waves.size()) {
        qDebug() << "No more waves to start - wave index" << m_currentWaveIndex << ">==" << m_waves.size();
        return;
    }

    // 初始化新波次
    m_waveInProgress     = true;
    m_timeSinceLastSpawn = 0.0F;
    m_enemiesSpawned     = 0;

    // 获取当前波次数据并显示详细信息
    WaveData const& currentWave = m_waves[m_currentWaveIndex];
    qDebug() << "Starting wave" << (m_currentWaveIndex + 1) << "of" << m_waves.size();
    qDebug() << "Wave has" << currentWave.enemies.size() << "enemies";
    qDebug() << "Spawn interval:" << currentWave.spawnInterval << "seconds";
    qDebug() << "Path index:" << currentWave.pathIndex;
    qDebug() << "=== END WAVE MANAGER START NEXT WAVE ===";
}

// 检查是否所有波次都已完成
bool WaveManager::isAllWavesCompleted() const {
    // 如果当前波次索引无效（-1），则波次尚未开始
    if (m_currentWaveIndex < 0) {
        return false;
    }

    // 只有当前波次不在进行中，且当前波次索引已经超过最后一波时，才算完成
    return !m_waveInProgress && static_cast<size_t>(m_currentWaveIndex) >= m_waves.size();
}

// 获取当前波次索引
int WaveManager::getCurrentWaveIndex() const {
    return m_currentWaveIndex;
}

// 获取总波次数
int WaveManager::getTotalWaves() const {
    return static_cast<int>(m_waves.size());
}

// 是否有波次正在进行
bool WaveManager::isWaveInProgress() const {
    return m_waveInProgress;
}

// 加载波次数据
void WaveManager::loadWaveData() {
    // 这里应该从配置文件或者地图数据中加载波次信息
    // 下面是简单的示例波次数据

    // 为了保持与当前代码结构一致，先仅使用一种敌人类型（MELEE）
    // 第一波：5个敌人
    WaveData wave1;
    wave1.spawnInterval = 5.0F;
    wave1.pathIndex     = 0;
    for (int i = 0; i < 5; i++) {
        wave1.enemies.push_back(EnemyType::MELEE);
    }
    m_waves.push_back(wave1);

    // 第二波：8个敌人
    WaveData wave2;
    wave2.spawnInterval = 5.0F;
    wave2.pathIndex     = 0;
    for (int i = 0; i < 8; i++) {
        wave2.enemies.push_back(EnemyType::MELEE);
    }
    m_waves.push_back(wave2);

    // 第三波：12个敌人
    WaveData wave3;
    wave3.spawnInterval = 5.0F;
    wave3.pathIndex     = 0;
    for (int i = 0; i < 12; i++) {
        wave3.enemies.push_back(EnemyType::MELEE);
    }
    m_waves.push_back(wave3);

    // 第四波：15个敌人
    WaveData wave4;
    wave4.spawnInterval = 5.0F;
    wave4.pathIndex     = 0;
    for (int i = 0; i < 15; i++) {
        wave4.enemies.push_back(EnemyType::MELEE);
    }
    m_waves.push_back(wave4);

    qDebug() << "Loaded" << m_waves.size() << "waves of enemies";
}

// 生成敌人
void WaveManager::spawnEnemy(EnemyType type, int pathIndex) {
    qDebug() << "WaveManager::spawnEnemy called with type" << static_cast<int>(type) << "on path" << pathIndex;
    std::shared_ptr<Unit> enemy = m_unitManager.spawnUnit(type, pathIndex);
    if (!enemy) {
        qWarning() << "Failed to spawn enemy of type" << static_cast<int>(type);
    } else {
        qDebug() << "Successfully spawned enemy with ID" << enemy->getId();
    }
}