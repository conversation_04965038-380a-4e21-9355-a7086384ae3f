#include "TowerDefense/GameLogic/UnitManager.hpp"

#include "TowerDefense/Core/AnimationEventSystem.hpp"
#include "TowerDefense/Core/GameController.hpp"
#include "TowerDefense/Core/GameData.hpp"
#include "TowerDefense/GameLogic/Enemy.hpp"
#include "TowerDefense/GameLogic/Map.hpp"
#include "TowerDefense/GameLogic/Tower.hpp"
#include "TowerDefense/GameLogic/Unit.hpp"
#include "TowerDefense/GameLogic/UnitFactory.hpp"
#include "TowerDefense/Types.hpp"

#include <QDebug>
#include <QPoint>
#include <algorithm>

UnitManager::UnitManager(Map const& map, GameController* gameController)
    : m_map(&map), m_lastUnitId(0), m_gameController(gameController) {
    qDebug() << "UnitManager created, referencing Map" << (m_gameController != nullptr ? " and GameController." : ".");

    // 初始化敌人工厂
    m_enemyFactory = std::make_unique<EnemyFactory>(map);
}

UnitID UnitManager::generateUnitId() {
    return ++m_lastUnitId;
}

void UnitManager::update(float deltaTime) {
    // 更新所有单位
    for (auto it = m_activeUnits.begin(); it != m_activeUnits.end();) {
        (*it)->update(deltaTime);

        if ((*it)->isDead()) {
            handleUnitDeath(**it);
            it = m_activeUnits.erase(it);
        } else if ((*it)->hasEscaped()) {
            handleUnitEscape(**it);
            it = m_activeUnits.erase(it);
        } else {
            ++it;
        }
    }

    // 检测塔和敌人之间的交互（塔攻击范围内的敌人）
    for (auto& unit : m_activeUnits) {
        if (unit->getType() == UnitType::TOWER_MELEE || unit->getType() == UnitType::TOWER_RANGED) {
            auto tower = std::dynamic_pointer_cast<Tower>(unit);
            if (tower) {
                tower->findAndAttackTarget(m_activeUnits);
            }
        }
    }

    // 敌人攻击近战塔逻辑
    for (auto& unit : m_activeUnits) {
        if (unit->getType() != UnitType::ENEMY_MELEE) {
            continue;
        }

        auto enemyPtr = std::dynamic_pointer_cast<Enemy>(unit);
        if (!enemyPtr) {
            continue;
        }

        // Cooldown 管理
        float& cd = m_enemyAttackCooldown[enemyPtr->getId()];
        if (cd > 0.0F) {
            cd -= deltaTime;
        }

        // 获取路径向量
        auto const& path = m_map->getPath(enemyPtr->getPathIndex());
        if (path.empty()) {
            continue;
        }

        int waypointIdx = enemyPtr->getWaypointIndex();
        if (static_cast<size_t>(waypointIdx + 1) >= path.size()) {
            continue;  // 已在终点附近
        }

        int nextGridX = static_cast<int>(path[waypointIdx + 1].x());
        int nextGridY = static_cast<int>(path[waypointIdx + 1].y());

        if (!m_map->isBlocked(nextGridX, nextGridY)) {
            continue;  // 没有被阻挡，无需攻击
        }

        // 获取阻挡此格子的塔
        std::shared_ptr<Unit> blockUnit = getUnitAt(QPoint(nextGridX, nextGridY));
        if (!blockUnit
            || (blockUnit->getType() != UnitType::TOWER_MELEE && blockUnit->getType() != UnitType::TOWER_RANGED)) {
            continue;
        }

        if (cd > 0.0F) {
            continue;  // 冷却中
        }

        // 攻击处理
        auto const& stats = GameData::getInstance().getStatsForEnemy(enemyPtr->getEnemyType());
        int damage        = stats.attackDamage;
        float interval    = stats.attackInterval;

        blockUnit->setHealth(blockUnit->getHealth() - damage);

        cd = interval;

        // 通知动画事件系统
        AnimationEventSystem::getInstance().notifyTowerAttack(enemyPtr->getId(),
                                                              enemyPtr->getPosition(),
                                                              blockUnit->getId(),
                                                              blockUnit->getPosition(),
                                                              damage,
                                                              enemyPtr->getType());

        // 若塔死亡，马上处理
        if (blockUnit->getHealth() <= 0) {
            handleUnitDeath(*blockUnit);
            // 从活动列表中延后删除，标记删除后循环外处理
        }
    }
}

// 生成一个新的敌方单位
std::shared_ptr<Unit> UnitManager::spawnUnit(EnemyType type, int pathIndex) {
    qDebug() << "=== UNIT MANAGER SPAWN UNIT ===";
    qDebug() << "Enemy type:" << static_cast<int>(type);
    qDebug() << "Path index:" << pathIndex;
    qDebug() << "Map path count:" << m_map->getPathCount();

    // 检查路径索引是否有效
    if (static_cast<size_t>(pathIndex) >= m_map->getPathCount()) {
        qDebug() << "Error: Attempted to spawn unit on invalid path index:" << pathIndex;
        return nullptr;
    }

    // 检查敌人工厂是否存在
    if (!m_enemyFactory) {
        qWarning() << "Error: EnemyFactory is null!";
        return nullptr;
    }

    // 使用敌人工厂创建单位
    UnitID newId = generateUnitId();
    qDebug() << "Generated unit ID:" << newId;
    std::shared_ptr<Unit> newUnit = m_enemyFactory->createEnemy(type, pathIndex, newId);

    if (!newUnit) {
        qWarning() << "EnemyFactory failed to create enemy!";
        return nullptr;
    }

    // 将新创建的单位添加到活动单位列表中
    m_activeUnits.push_back(newUnit);
    qDebug() << "Added enemy to active units list. Total active units:" << m_activeUnits.size();
    qDebug() << "Enemy position:" << newUnit->getPosition().x() << "," << newUnit->getPosition().y();

    // 发送敌人生成动画事件
    AnimationEventSystem::getInstance().notifyEnemySpawned(
        newUnit->getId(), newUnit->getPosition(), newUnit->getType());

    qDebug() << "Successfully spawned enemy of type" << static_cast<int>(type) << "on path" << pathIndex;
    qDebug() << "=== END UNIT MANAGER SPAWN UNIT ===";
    return newUnit;
}

// 在地图上放置一个塔
std::shared_ptr<Unit> UnitManager::placeTower(TowerType type, QPoint const& position) {
    // 检查位置是否有效
    if (!m_map->isBuildableAt(position)) {
        qDebug() << "Error: Cannot place tower at position" << position << "(not buildable)";
        return nullptr;
    }

    // 检查该位置是否已有单位
    if (getUnitAt(position)) {
        qDebug() << "Error: Cannot place tower at position" << position << "(occupied)";
        return nullptr;
    }

    // 将网格坐标转换为世界坐标（瓦片中心），以保证与敌人位置坐标系一致
    QPointF worldPos = m_map->gridToWorld(position.x(), position.y());

    // 使用塔工厂创建塔，位置采用世界坐标
    std::shared_ptr<Unit> newUnit = TowerFactory::createTower(type, worldPos, generateUnitId());

    // 将新创建的塔添加到活动单位列表中
    m_activeUnits.push_back(newUnit);
    qDebug() << "Placed tower of type" << static_cast<int>(type) << "at position" << position;

    // 如果是近战塔，则标记该格子被阻挡
    if (type == TowerType::MELEE) {
        m_map->setBlocked(position.x(), position.y(), true);
    }

    // 发送塔建造动画事件
    AnimationEventSystem::getInstance().notifyTowerBuilt(newUnit->getId(), newUnit->getPosition(), newUnit->getType());

    return newUnit;
}

// 移除指定位置的塔
bool UnitManager::removeTower(QPoint const& position) {
    // 遍历活动单位列表，查找指定位置的塔
    auto it = std::ranges::find_if(m_activeUnits, [this, &position](std::shared_ptr<Unit> const& unit) {
        if (unit->getType() == UnitType::TOWER_MELEE || unit->getType() == UnitType::TOWER_RANGED) {
            // 单位位置以世界坐标存储，需要转换回网格坐标进行比较
            QPoint gridPos = m_map->worldToGrid(unit->getPosition());
            return gridPos == position;
        }
        return false;
    });

    // 如果找到塔，则从活动单位列表中移除
    if (it != m_activeUnits.end()) {
        // 在删除前获取塔的信息以发送通知
        UnitID towerId     = (*it)->getId();
        QPointF towerPos   = (*it)->getPosition();
        UnitType towerType = (*it)->getType();

        // 若为近战塔，解除阻挡
        if (towerType == UnitType::TOWER_MELEE) {
            QPoint gridPos = m_map->worldToGrid(towerPos);
            m_map->setBlocked(gridPos.x(), gridPos.y(), false);
        }

        m_activeUnits.erase(it);
        qDebug() << "Removed tower at position" << position;

        // 发送塔被摧毁的动画事件
        AnimationEventSystem::getInstance().notifyTowerDestroyed(towerId, towerPos, towerType);

        return true;
    }

    // 如果未找到塔，则返回 false
    qDebug() << "Error: No tower found at position" << position;
    return false;
}

// 获取指定位置的单位
std::shared_ptr<Unit> UnitManager::getUnitAt(QPoint const& position) const {
    // 遍历活动单位列表，查找指定位置的单位
    for (auto const& unit : m_activeUnits) {
        // 单位位置以世界坐标存储，需要转换回网格坐标进行比较
        QPoint gridPos = m_map->worldToGrid(unit->getPosition());
        if (gridPos == position) {
            return unit;
        }
    }
    return nullptr;
}

// 获取指定类型的所有单位
std::vector<std::shared_ptr<Unit>> UnitManager::getUnitsByType(UnitType type) const {
    std::vector<std::shared_ptr<Unit>> result;
    for (auto const& unit : m_activeUnits) {
        if (unit->getType() == type) {
            result.push_back(unit);
        }
    }
    return result;
}

// 获取所有的塔
std::vector<std::shared_ptr<Unit>> UnitManager::getAllTowers() const {
    std::vector<std::shared_ptr<Unit>> result;
    for (auto const& unit : m_activeUnits) {
        if (unit->getType() == UnitType::TOWER_MELEE || unit->getType() == UnitType::TOWER_RANGED) {
            result.push_back(unit);
        }
    }
    return result;
}

// 获取所有的敌人
std::vector<std::shared_ptr<Unit>> UnitManager::getAllEnemies() const {
    std::vector<std::shared_ptr<Unit>> result;
    for (auto const& unit : m_activeUnits) {
        if (unit->getType() == UnitType::ENEMY_MELEE) {
            result.push_back(unit);
        }
    }
    return result;
}

// 获取所有活跃单位
std::vector<std::shared_ptr<Unit>> const& UnitManager::getActiveUnits() const {
    return m_activeUnits;
}

// 处理单位逃脱（到达终点）
bool UnitManager::handleUnitEscape(Unit& unit) {
    qDebug() << "Unit" << unit.getId() << "escaped!";

    // 发送敌人逃脱的动画事件
    if (unit.getType() == UnitType::ENEMY_MELEE) {
        AnimationEventSystem::getInstance().notifyEnemyEscaped(unit.getId(), unit.getPosition(), unit.getType());
    }

    // 通知 GameController 敌人到达终点，扣除玩家生命值
    if (m_gameController != nullptr && (unit.getType() == UnitType::ENEMY_MELEE)) {
        auto* enemy = dynamic_cast<Enemy*>(&unit);
        if (enemy != nullptr) {
            try {
                // 从 GameData 获取敌人逃脱时的惩罚值
                auto const& stats = GameData::getInstance().getStatsForEnemy(enemy->getEnemyType());
                int livesToLose   = stats.escapePenalty;

                // 调用 GameController 的方法扣除生命值
                m_gameController->loseLives(livesToLose);

                qDebug() << "Enemy" << enemy->getId() << "escaped: Player lost" << livesToLose
                         << "lives based on GameData";
            } catch (std::exception const& e) {
                qWarning() << "Failed to get escape penalty from GameData:" << e.what();
                // 默认扣除1条生命
                m_gameController->loseLives(1);
                qDebug() << "Enemy" << enemy->getId() << "escaped: Player lost 1 life (default)";
            }
        }
    }
    return true;
}

// 处理单位死亡
void UnitManager::handleUnitDeath(Unit& unit) {
    qDebug() << "Unit" << unit.getId() << "died!";

    // 根据单位类型分别处理敌人和塔的死亡
    if (unit.getType() == UnitType::ENEMY_MELEE) {
        // 敌人死亡处理
        auto* enemy = dynamic_cast<Enemy*>(&unit);
        if (enemy != nullptr) {
            // 发送敌人被杀死的动画事件
            AnimationEventSystem::getInstance().notifyEnemyKilled(unit.getId(), unit.getPosition(), unit.getType());

            // 如果GameController可用，通知处理敌人死亡奖励
            if (m_gameController != nullptr) {
                // 通知 GameController 处理敌人死亡
                m_gameController->processEnemyDeath(enemy);
                qDebug() << "Enemy died: GameController processing enemy death";
            }
        }
    } else if (unit.getType() == UnitType::TOWER_MELEE || unit.getType() == UnitType::TOWER_RANGED) {
        // 塔被摧毁处理
        auto* tower = dynamic_cast<Tower*>(&unit);
        if (tower != nullptr) {
            // 发送塔被摧毁的动画事件
            AnimationEventSystem::getInstance().notifyTowerDestroyed(unit.getId(), unit.getPosition(), unit.getType());

            // 如果GameController可用，通知处理塔被摧毁
            if (m_gameController != nullptr) {
                m_gameController->processTowerDeath(tower);
                qDebug() << "Tower died: GameController processing tower death";
            }

            // 若为近战塔，解除阻挡
            if (tower->getTowerType() == TowerType::MELEE) {
                QPoint gridPos = m_map->worldToGrid(tower->getPosition());
                m_map->setBlocked(gridPos.x(), gridPos.y(), false);
            }
        }
    }
}

// 清空所有单位（如关卡结束或重新开始时）
void UnitManager::clearAllUnits() {
    m_activeUnits.clear();
    qDebug() << "All units cleared.";
}

// 获取指定ID的单位（如果存在）
std::shared_ptr<Unit> UnitManager::getUnitById(UnitID id) const {
    for (auto const& unit : m_activeUnits) {
        if (unit->getId() == id) {
            return unit;
        }
    }
    return nullptr;
}