#include "TowerDefense/GameLogic/Tower.hpp"

#include "TowerDefense/Core/AnimationEventSystem.hpp"
#include "TowerDefense/Core/GameData.hpp"
#include "TowerDefense/GameLogic/Unit.hpp"
#include "TowerDefense/Types.hpp"

#include <QDebug>
#include <cmath>

#ifndef M_PI
#    define M_PI 3.14159265358979323846
#endif

// 使用AnimationEventSystem
using AnimationEventSystem = AnimationEventSystem;

// Tower类实现

// 构造函数
Tower::Tower(UnitID id,
             TowerType towerType,
             UnitType unitType,
             QPointF const& position,
             int healthPoints,                           // 健康点数
             float attackRange,                          // 攻击范围
             int attackDamage,                           // 攻击伤害
             float attackInterval)                       // 攻击间隔
    : Unit(id, unitType, position, healthPoints, 0.0F),  // 塔的速度为0
      m_towerType(towerType),
      m_attackRange(attackRange),
      m_attackDamage(attackDamage),
      m_attackInterval(attackInterval) {
    // 初始化旋转与弹药系统支持信息
    try {
        auto const& visual = GameData::getInstance().getVisualDataForTower(towerType);
        m_supportRotation  = visual.rotationSupport;
        m_hasAmmoSystem    = visual.hasAmmoSystem;
        m_ammoLoaded       = true;  // 初始装填完毕
    } catch (std::exception const& e) {
        // 如果 GameData 未正确初始化，保持默认值
        qWarning() << "Tower ctor: failed to query visual data:" << e.what();
    }
}

void Tower::update(float deltaTime) {
    // 更新攻击冷却
    if (m_attackCooldown > 0.0F) {
        m_attackCooldown -= deltaTime;
    }

    // 如果有弹药系统且当前未装弹，而冷却已经结束，则自动装弹
    if (m_hasAmmoSystem && !m_ammoLoaded && m_attackCooldown <= 0.0F) {
        m_ammoLoaded                        = true;
        constexpr float RELOAD_DISPLAY_TIME = 0.25f;                // 装弹完成后等待的显示时间
        m_attackCooldown                    = RELOAD_DISPLAY_TIME;  // 保持"已装弹"状态一小段时间
        // 这里可以发送弹药装填完成动画事件
    }

    // 更新组件
    for (auto const& component : m_components) {
        component->update(deltaTime);
    }
}

bool Tower::isDead() const {
    return m_health <= 0;
}

bool Tower::addAffix(std::shared_ptr<Affix> const& affix) {
    if (m_affixes.size() >= MAX_AFFIXES) {
        return false;  // 无法添加更多词缀
    }

    m_affixes.push_back(affix);
    return true;
}

bool Tower::removeAffix(unsigned int affixIndex) {
    if (affixIndex >= m_affixes.size()) {
        return false;  // 索引无效
    }

    m_affixes.erase(m_affixes.begin() + affixIndex);
    return true;
}

std::vector<std::shared_ptr<Affix>> const& Tower::getAffixes() const {
    return m_affixes;
}

// MeleeTower类实现

MeleeTower::MeleeTower(UnitID id, QPointF const& position)
    : Tower(id, TowerType::MELEE, UnitType::TOWER_MELEE, position, 200, 1.0F, 30, 1.0F) {
    try {
        // 从 GameData 获取塔的属性
        auto const& stats = GameData::getInstance().getStatsForTower(TowerType::MELEE);

        // 使用 GameData 中的数据设置塔的属性
        setHealth(stats.maxHealth);
        m_attackRange    = stats.attackRange;
        m_attackDamage   = stats.attackDamage;
        m_attackInterval = stats.attackInterval;

        qDebug() << "Created MeleeTower with ID:" << id << "at position:" << position << "| HP:" << stats.maxHealth
                 << "| DMG:" << stats.attackDamage << "| Range:" << stats.attackRange
                 << "| Interval:" << stats.attackInterval;
    } catch (std::exception const& e) {
        qWarning() << "Failed to get MeleeTower stats, using default values:" << e.what();
    }
}

float MeleeTower::getAttackRange() const {
    try {
        return GameData::getInstance().getStatsForTower(TowerType::MELEE).attackRange;
    } catch (std::exception const&) {
        return m_attackRange;  // 使用默认值作为备用
    }
}

int MeleeTower::getAttackDamage() const {
    try {
        return GameData::getInstance().getStatsForTower(TowerType::MELEE).attackDamage;
    } catch (std::exception const&) {
        return m_attackDamage;  // 使用默认值作为备用
    }
}

float MeleeTower::getAttackInterval() const {
    try {
        return GameData::getInstance().getStatsForTower(TowerType::MELEE).attackInterval;
    } catch (std::exception const&) {
        return m_attackInterval;  // 使用默认值作为备用
    }
}

int MeleeTower::getBuildCost() const {
    try {
        return GameData::getInstance().getStatsForTower(TowerType::MELEE).buildCost;
    } catch (std::exception const&) {
        return 100;  // 使用默认值作为备用
    }
}

int MeleeTower::getSellValue() const {
    try {
        return GameData::getInstance().getStatsForTower(TowerType::MELEE).sellValue;
    } catch (std::exception const&) {
        return 50;  // 使用默认值作为备用
    }
}

void MeleeTower::findAndAttackTarget(std::vector<std::shared_ptr<Unit>> const& units) {
    // 没有装弹或处于冷却时无法攻击
    if (m_attackCooldown > 0.0F || !isAmmoLoaded()) {
        return;  // 攻击冷却中
    }

    // 寻找范围内的第一个敌人
    for (auto const& unit : units) {
        // 只攻击敌人类型
        if (unit->getType() == UnitType::ENEMY_MELEE) {
            // 计算距离
            QPointF diff  = unit->getPosition() - m_position;
            auto distance = static_cast<float>(std::sqrt((diff.x() * diff.x()) + (diff.y() * diff.y())));

            if (distance <= m_attackRange) {
                // 设置旋转角度
                if (m_supportRotation) {
                    m_rotation = static_cast<float>(std::atan2(diff.y(), diff.x()) * 180.0 / M_PI);
                }

                // 在攻击范围内，执行攻击
                unit->setHealth(unit->getHealth() - m_attackDamage);
                m_attackCooldown = m_attackInterval;  // 重置冷却
                if (m_hasAmmoSystem) {
                    m_ammoLoaded = false;  // 发射后弹药为空
                }
                qDebug() << "MeleeTower" << m_id << "attacked unit" << unit->getId() << "for" << m_attackDamage
                         << "damage";

                // 发送攻击事件
                AnimationEventSystem::getInstance().notifyTowerAttack(
                    m_id, m_position, unit->getId(), unit->getPosition(), m_attackDamage, m_type);

                // 发送敌人受伤事件
                AnimationEventSystem::getInstance().notifyEnemyDamageTaken(
                    unit->getId(), unit->getPosition(), m_attackDamage, unit->getHealth(), unit->getType());

                return;  // 一次只攻击一个目标
            }
        }
    }
}

// RangedArrowTower类实现

RangedArrowTower::RangedArrowTower(UnitID id, QPointF const& position)
    : Tower(id, TowerType::RANGED, UnitType::TOWER_RANGED, position, 150, 3.0F, 20, 1.5F) {
    try {
        // 从 GameData 获取塔的属性
        auto const& stats = GameData::getInstance().getStatsForTower(TowerType::RANGED);

        // 使用 GameData 中的数据设置塔的属性
        setHealth(stats.maxHealth);
        m_attackRange    = stats.attackRange;
        m_attackDamage   = stats.attackDamage;
        m_attackInterval = stats.attackInterval;

        qDebug() << "Created RangedArrowTower with ID:" << id << "at position:" << position
                 << "| HP:" << stats.maxHealth << "| DMG:" << stats.attackDamage << "| Range:" << stats.attackRange
                 << "| Interval:" << stats.attackInterval;
    } catch (std::exception const& e) {
        qWarning() << "Failed to get RangedArrowTower stats, using default values:" << e.what();
    }
}

float RangedArrowTower::getAttackRange() const {
    try {
        return GameData::getInstance().getStatsForTower(TowerType::RANGED).attackRange;
    } catch (std::exception const&) {
        return m_attackRange;  // 使用默认值作为备用
    }
}

int RangedArrowTower::getAttackDamage() const {
    try {
        return GameData::getInstance().getStatsForTower(TowerType::RANGED).attackDamage;
    } catch (std::exception const&) {
        return m_attackDamage;  // 使用默认值作为备用
    }
}

float RangedArrowTower::getAttackInterval() const {
    try {
        return GameData::getInstance().getStatsForTower(TowerType::RANGED).attackInterval;
    } catch (std::exception const&) {
        return m_attackInterval;  // 使用默认值作为备用
    }
}

int RangedArrowTower::getBuildCost() const {
    try {
        return GameData::getInstance().getStatsForTower(TowerType::RANGED).buildCost;
    } catch (std::exception const&) {
        return 150;  // 使用默认值作为备用
    }
}

int RangedArrowTower::getSellValue() const {
    try {
        return GameData::getInstance().getStatsForTower(TowerType::RANGED).sellValue;
    } catch (std::exception const&) {
        return 75;  // 使用默认值作为备用
    }
}

void RangedArrowTower::findAndAttackTarget(std::vector<std::shared_ptr<Unit>> const& units) {
    // 没有装弹或处于冷却时无法攻击
    if (m_attackCooldown > 0.0F || !isAmmoLoaded()) {
        return;  // 攻击冷却中
    }

    // 寻找范围内的第一个敌人
    for (auto const& unit : units) {
        // 只攻击敌人类型
        if (unit->getType() == UnitType::ENEMY_MELEE) {
            // 计算距离
            QPointF diff  = unit->getPosition() - m_position;
            auto distance = static_cast<float>(std::sqrt((diff.x() * diff.x()) + (diff.y() * diff.y())));

            if (distance <= m_attackRange) {
                // 设置旋转角度
                if (m_supportRotation) {
                    m_rotation = static_cast<float>(std::atan2(diff.y(), diff.x()) * 180.0 / M_PI);
                }

                // 在攻击范围内，执行攻击
                unit->setHealth(unit->getHealth() - m_attackDamage);
                m_attackCooldown = m_attackInterval;  // 重置冷却
                if (m_hasAmmoSystem) {
                    m_ammoLoaded = false;  // 发射后弹药为空
                }
                qDebug() << "RangedArrowTower" << m_id << "attacked unit" << unit->getId() << "for" << m_attackDamage
                         << "damage";

                // 发送攻击事件
                AnimationEventSystem::getInstance().notifyTowerAttack(
                    m_id, m_position, unit->getId(), unit->getPosition(), m_attackDamage, m_type);

                // 发送敌人受伤事件
                AnimationEventSystem::getInstance().notifyEnemyDamageTaken(
                    unit->getId(), unit->getPosition(), m_attackDamage, unit->getHealth(), unit->getType());

                return;  // 一次只攻击一个目标
            }
        }
    }
}

// RangedCannonTower类已移除 - 功能通过词缀系统实现