#include "TowerDefense/GameLogic/Map.hpp"

#include "TowerDefense/Types.hpp"

#include <QDateTime>
#include <QDebug>
#include <QFile>
#include <QStringList>
#include <QTextStream>
#include <QVariant>
#include <array>
#include <cmath>     // Added for std::floor
#include <iterator>  // Required for std::inserter
#include <set>       // Added for validation

// 匿名命名空间，用于定义仅在当前文件中使用的辅助函数
namespace {
// 将瓦片字符转换为 TileType 枚举
TileType tileCharToType(QChar tileChar) {
    if (tileChar == 'G') {
        return TileType::BUILDABLE_GROUND;
    }
    if (tileChar == 'P') {
        return TileType::PATH;
    }
    if (tileChar == 'S') {
        return TileType::START_POINT;
    }
    if (tileChar == 'E') {
        return TileType::END_POINT;
    }
    if (tileChar == 'W') {
        return TileType::OBSTACLE;
    }
    return TileType::INVALID;
}

// 辅助函数: TileType -> 字符
char tileTypeToChar(TileType type) {
    switch (type) {
        case TileType::BUILDABLE_GROUND:
            return 'G';
        case TileType::PATH:
            return 'P';
        case TileType::START_POINT:
            return 'S';
        case TileType::END_POINT:
            return 'E';
        case TileType::OBSTACLE:
            return 'W';
        default:
            return '?';
    }
}
}  // anonymous namespace

// 从文件加载地图
bool Map::loadFromFile(std::string const& filePath) {
    QFile file(QString::fromStdString(filePath));
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "Failed to open file: " << QString::fromStdString(filePath);
        return false;
    }

    QTextStream in(&file);
    // 初始化地图数据
    resetMapData();

    // 解析文件内容
    if (!parseFileContent(in)) {
        file.close();
        return false;
    }

    // 验证基本地图属性
    if (!validateBasicProperties()) {
        file.close();
        return false;
    }

    // 验证路径和瓦片一致性
    if (!validatePathConsistency()) {
        file.close();
        return false;
    }

    file.close();
    qDebug() << "Successfully loaded and validated map:" << QString::fromStdString(filePath) << "Dimensions:" << m_width
             << "x" << m_height << "Tile Size:" << m_tileSize << "Number of Paths:" << m_paths.size();
    return true;
}

// 重置地图数据
void Map::resetMapData() {
    m_paths.clear();
    m_tiles.clear();
    m_width    = 0;
    m_height   = 0;
    m_tileSize = 0.0F;
}

// 解析文件内容
bool Map::parseFileContent(QTextStream& in) {
    QString currentSection;
    while (!in.atEnd()) {
        QString line        = in.readLine();
        QString trimmedLine = line.trimmed();

        // 跳过空行和注释
        if (trimmedLine.isEmpty() || trimmedLine.startsWith('#')) {
            continue;
        }

        // 检查节头
        if (trimmedLine.startsWith('[') && trimmedLine.endsWith(']')) {
            currentSection = trimmedLine.mid(1, trimmedLine.length() - 2);
            qDebug() << "Parsing section:" << currentSection;
            continue;
        }

        // 解析当前节的数据
        if (!parseSectionLine(currentSection, line, trimmedLine)) {
            return false;
        }
    }
    return true;
}

// 解析节数据行
bool Map::parseSectionLine(QString const& section, QString const& line, QString const& trimmedLine) {
    if (section == "Metadata") {
        return parseMetadataLine(this, trimmedLine);
    }
    if (section == "Tiles") {
        return parseTilesLine(this, trimmedLine);
    }
    if (section == "Paths") {
        return parsePathsLine(this, line);  // parsePathsLine 需要处理行内注释
    }

    // 未知节，发出警告并继续
    qDebug() << "Warning: Skipping line in unknown section:" << section << ", line:" << line;
    return true;
}

// 解析元数据行
bool parseMetadataLine(Map* map, QString const& line) {
    QString cleanLine = line.split('#', Qt::SkipEmptyParts).first().trimmed();

    QStringList parts = cleanLine.split('=', Qt::SkipEmptyParts);
    if (parts.size() == 2) {
        QString key   = parts[0].trimmed();
        QString value = parts[1].trimmed();

        if (key == "name") {
            // 地图名称可以存储在成员变量中
            // map->m_name = value;
        } else if (key == "width") {
            map->m_width = value.toInt();
        } else if (key == "height") {
            map->m_height = value.toInt();
        } else if (key == "tileSize") {
            map->m_tileSize = value.toFloat();
        }
        //TODO:处理其他元数据，如初始生命值、初始金币等
        return true;  // 成功解析元数据行
    }
    // 这行不是元数据中的键值对，可能是注释或空行已经跳过
    // 或者它可能是错误，取决于所需的严格性。现在，将其视为警告/跳过。
    qDebug() << "Warning: Skipping invalid metadata line:" << line;
    return true;  // 仍然返回 true 以继续处理
}

// 解析瓦片行
bool parseTilesLine(Map* map, QString const& line) {
    QString cleanLine = line.split('#', Qt::SkipEmptyParts).first().trimmed();

    if (map->m_width == 0 || map->m_height == 0) {
        qDebug() << "Error: Map dimensions (width/height) must be defined before Tiles section.";
        return false;  // 指示错误
    }
    // 确保行具有正确的宽度
    if (cleanLine.length() != map->m_width) {
        qDebug() << "Error: Tile row width mismatch. Expected" << map->m_width << ", got" << cleanLine.length()
                 << "in line:" << line;
        return false;  // 指示错误
    }

    std::vector<TileType> row;
    row.reserve(map->m_width);  // 预留空间以提高效率
    for (QChar tileChar : cleanLine) {
        row.push_back(tileCharToType(tileChar));
    }
    map->m_tiles.push_back(row);
    return true;  // 成功解析瓦片行
}

// 解析路径行
bool parsePathsLine(Map* map, QString const& line) {
    // 忽略包含 # 的注释部分
    QString cleanLine = line.split('#', Qt::SkipEmptyParts).first().trimmed();

    // 如果是空行，跳过
    if (cleanLine.isEmpty()) {
        return true;
    }

    // 检查是否是新路径声明（Path:ID 格式）
    if (cleanLine.startsWith("Path:", Qt::CaseInsensitive)) {
        // 创建新路径
        map->m_paths.emplace_back();
        int pathId = -1;

        // 尝试提取路径ID
        QString idStr = cleanLine.mid(5).trimmed();
        bool ok       = false;
        pathId        = idStr.toInt(&ok);

        if (ok) {
            qDebug() << "Starting new path with ID:" << pathId;
        } else {
            qDebug() << "Starting new path with unspecified ID";
        }

        return true;
    }

    // 如果不是路径声明，则处理为坐标点
    QStringList coords = cleanLine.split(',', Qt::SkipEmptyParts);
    if (coords.size() == 2) {
        bool okX = false;
        bool okY = false;
        int x    = coords[0].toInt(&okX);
        int y    = coords[1].toInt(&okY);

        if (okX && okY) {
            // 检查坐标是否在地图范围内
            if (x < 0 || x >= map->m_width || y < 0 || y >= map->m_height) {
                qDebug() << "Error: Path waypoint" << x << "," << y << "outside map bounds" << map->m_width << "x"
                         << map->m_height;
                return false;
            }

            // 检查该位置是否是有效路径瓦片
            if (map->m_tiles.size() > static_cast<size_t>(y) && map->m_tiles[y].size() > static_cast<size_t>(x)) {
                TileType tileType = map->m_tiles[y][x];
                if (tileType != TileType::PATH && tileType != TileType::START_POINT
                    && tileType != TileType::END_POINT) {
                    qDebug() << "Warning: Path waypoint" << x << "," << y << "is not on a valid path tile";
                }
            }

            // 确保有一个路径可以添加点
            if (map->m_paths.empty()) {
                qDebug() << "Warning: Adding waypoint without a defined path, creating default path";
                map->m_paths.emplace_back();
            }

            map->m_paths.back().emplace_back(static_cast<float>(x), static_cast<float>(y));
            return true;
        }
        qDebug() << "Error: Invalid waypoint coordinates in line:" << line;
        return false;
    }

    qDebug() << "Warning: Skipping line in Paths section with unexpected format:" << line;
    return true;
}

// 验证基本地图属性
bool Map::validateBasicProperties() {
    if (m_width <= 0 || m_height <= 0 || m_tileSize <= 0.0F) {
        qDebug() << "Error: Map dimensions or tile size not properly defined in Metadata.";
        return false;
    }
    if (m_tiles.size() != static_cast<size_t>(m_height)) {
        qDebug() << "Error: Tile row count mismatch. Expected" << m_height << ", got" << m_tiles.size();
        return false;
    }
    if (m_paths.empty()) {
        qDebug() << "Error: No paths defined in the map file.";
        return false;
    }

    // 添加对可建造地面的验证 (temporarily make this a warning for testing)
    if (!validateBuildableGrounds()) {
        qWarning() << "Warning: Some buildable ground tiles are not adjacent to paths. This may affect gameplay.";
        // return false;  // Temporarily disabled for testing asset rendering
    }

    return true;
}

// 验证路径和瓦片一致性
bool Map::validatePathConsistency() {
    // 收集地图中的路径瓦片
    auto pathTilesInMap = collectPathTilesFromMap();

    // 收集所有路径中的航点
    auto waypointsInPaths = collectWaypointsFromPaths();

    // 检查航点是否对应路径瓦片
    if (!checkWaypointsValidity()) {
        return false;
    }

    // 比较路径瓦片和航点集合
    return comparePathTilesAndWaypoints(pathTilesInMap, waypointsInPaths);
}

// 收集地图中的所有路径瓦片
std::set<QPoint, QPointCompare> Map::collectPathTilesFromMap() {
    std::set<QPoint, QPointCompare> pathTilesInMap;

    for (int y = 0; y < m_height; ++y) {
        for (int x = 0; x < m_width; ++x) {
            TileType type = m_tiles[y][x];
            if (type == TileType::PATH || type == TileType::START_POINT || type == TileType::END_POINT) {
                pathTilesInMap.insert(QPoint(x, y));
            }
        }
    }

    return pathTilesInMap;
}

// 收集所有路径中的航点
std::set<QPoint, QPointCompare> Map::collectWaypointsFromPaths() {
    std::set<QPoint, QPointCompare> waypointsInPaths;

    for (auto const& path : m_paths) {
        for (auto const& waypointF : path) {
            QPoint waypoint(static_cast<int>(waypointF.x()), static_cast<int>(waypointF.y()));
            waypointsInPaths.insert(waypoint);
        }
    }

    return waypointsInPaths;
}

// 检查所有航点是否有效
bool Map::checkWaypointsValidity() {
    for (auto const& path : m_paths) {
        for (auto const& waypointF : path) {
            QPoint waypoint(static_cast<int>(waypointF.x()), static_cast<int>(waypointF.y()));

            // 验证1: 检查航点是否在地图边界内
            if (waypoint.x() < 0 || waypoint.x() >= m_width || waypoint.y() < 0 || waypoint.y() >= m_height) {
                qDebug() << "Error: Waypoint" << waypoint << "in paths is outside map bounds.";
                return false;
            }

            // 验证2: 检查航点是否对应一个路径瓦片
            if (m_tiles[waypoint.y()][waypoint.x()] != TileType::PATH
                && m_tiles[waypoint.y()][waypoint.x()] != TileType::START_POINT
                && m_tiles[waypoint.y()][waypoint.x()] != TileType::END_POINT) {
                qDebug() << "Error: Waypoint" << waypoint << "in paths does not correspond to a path tile in [Tiles].";
                return false;
            }
        }
    }

    return true;
}

// 比较路径瓦片和航点集合
bool Map::comparePathTilesAndWaypoints(std::set<QPoint, QPointCompare> const& pathTilesInMap,
                                       std::set<QPoint, QPointCompare> const& waypointsInPaths) {
    // 检查两个集合是否相等，直接使用 std::set::operator==
    bool setsEqual = pathTilesInMap == waypointsInPaths;

    if (!setsEqual) {
        qDebug() << "Error: Mismatch between path tiles in [Tiles] and waypoints in [Paths].";
        reportMissingPathTiles(pathTilesInMap, waypointsInPaths);
        reportExtraWaypoints(pathTilesInMap, waypointsInPaths);
        return false;
    }

    return true;
}

// 报告缺失的路径瓦片
void Map::reportMissingPathTiles(std::set<QPoint, QPointCompare> const& pathTilesInMap,
                                 std::set<QPoint, QPointCompare> const& waypointsInPaths) {
    std::set<QPoint, QPointCompare> missingInPaths;
    // 使用传统的 std::set_difference
    std::set_difference(pathTilesInMap.begin(),
                        pathTilesInMap.end(),
                        waypointsInPaths.begin(),
                        waypointsInPaths.end(),
                        std::inserter(missingInPaths, missingInPaths.begin()),
                        QPointCompare());

    if (!missingInPaths.empty()) {
        qDebug() << "  The following path tiles in [Tiles] are not included in any path in [Paths]:";
        for (auto const& p : missingInPaths) {
            qDebug() << "    (" << p.x() << "," << p.y() << ")";
        }
    }
}

// 报告多余的航点
void Map::reportExtraWaypoints(std::set<QPoint, QPointCompare> const& pathTilesInMap,
                               std::set<QPoint, QPointCompare> const& waypointsInPaths) {
    std::set<QPoint, QPointCompare> extraInPaths;
    // 使用传统的 std::set_difference
    std::set_difference(waypointsInPaths.begin(),
                        waypointsInPaths.end(),
                        pathTilesInMap.begin(),
                        pathTilesInMap.end(),
                        std::inserter(extraInPaths, extraInPaths.begin()),
                        QPointCompare());

    // 这个分支理论上不会被触发，因为验证2已经检查过
    if (!extraInPaths.empty()) {
        qDebug() << "  The following waypoints in [Paths] do not correspond to path tiles in [Tiles]:";
        for (auto const& p : extraInPaths) {
            qDebug() << "    (" << p.x() << "," << p.y() << ")";
        }
    }
}

// 将网格坐标 (x, y) 转换为世界位置（例如，瓦片中心）
QPointF Map::gridToWorld(int x, int y) const {
    // 返回瓦片中心的坐标
    return {(static_cast<float>(x) * m_tileSize) + (m_tileSize / 2.0F),
            (static_cast<float>(y) * m_tileSize) + (m_tileSize / 2.0F)};
}

// 将世界位置转换为网格坐标
// 返回一个 QPoint，表示世界位置所在的整数网格坐标
QPoint Map::worldToGrid(QPointF const& worldPos) const {
    // 使用 floor 确保即使对于负坐标也能正确地向下取整到网格索引
    // 如果世界坐标始终为非负，简单的 static_cast<int> 也可以
    int x = static_cast<int>(std::floor(worldPos.x() / m_tileSize));
    int y = static_cast<int>(std::floor(worldPos.y() / m_tileSize));

    // 返回整数网格坐标
    return {x, y};
}

// 获取特定网格坐标 (x, y) 的瓦片类型
TileType Map::getTileType(int x, int y) const {
    if (!isValidCoordinate(x, y)) {
        // 如果坐标无效，返回无效类型并打印警告
        qDebug() << "Warning: Attempted to get tile type for invalid coordinate:" << x << "," << y;
        return TileType::INVALID;
    }
    return m_tiles[y][x];
}

// 检查网格坐标 (x, y) 是否在地图边界内
bool Map::isValidCoordinate(int x, int y) const {
    return x >= 0 && x < m_width && y >= 0 && y < m_height;
}

// 检查指定坐标是否与路径相邻（包括起点和终点）
bool Map::isAdjacentToPath(int x, int y) const {
    // 检查上下左右四个方向
    static std::array<int, 4> const dx = {-1, 1, 0, 0};
    static std::array<int, 4> const dy = {0, 0, -1, 1};

    for (size_t i = 0; i < dx.size(); ++i) {
        int nx = x + dx[i];
        int ny = y + dy[i];

        if (isValidCoordinate(nx, ny)) {
            TileType type = getTileType(nx, ny);
            if (type == TileType::PATH || type == TileType::START_POINT || type == TileType::END_POINT) {
                return true;
            }
        }
    }
    return false;
}

// 检查特定网格坐标 (x, y) 的瓦片是否可建造指定类型的塔
bool Map::isBuildable(int x, int y, TowerType towerType) const {
    if (!isValidCoordinate(x, y)) {
        return false;
    }

    TileType tileType = getTileType(x, y);

    switch (towerType) {
        case TowerType::MELEE:
            // 近战塔只能部署在路径上
            return tileType == TileType::PATH || tileType == TileType::START_POINT || tileType == TileType::END_POINT;

        case TowerType::RANGED:
            // 远程塔只能部署在路径之外且与路径相邻的格子上
            return tileType == TileType::BUILDABLE_GROUND && isAdjacentToPath(x, y);

        default:
            return false;
    }
}

// 检查指定位置是否可以建造塔
bool Map::isBuildableAt(QPoint const& position) const {
    // 首先检查是否可以建造近战塔
    if (isBuildable(position.x(), position.y(), TowerType::MELEE)) {
        return true;
    }

    // 然后检查是否可以建造远程塔
    return isBuildable(position.x(), position.y(), TowerType::RANGED);
}

// 获取特定索引 (pathIndex) 的路径的所有航点
std::vector<QPointF> const& Map::getPath(int pathIndex) const {
    if (pathIndex >= 0 && static_cast<size_t>(pathIndex) < m_paths.size()) {
        return m_paths[pathIndex];
    }

    qDebug() << "Warning: Attempted to get path with invalid index:" << pathIndex;
    static std::vector<QPointF> const emptyPath;
    return emptyPath;
}

// 验证可建造地面的有效性
bool Map::validateBuildableGrounds() const {
    for (int y = 0; y < m_height; ++y) {
        for (int x = 0; x < m_width; ++x) {
            if (m_tiles[y][x] == TileType::BUILDABLE_GROUND && !isAdjacentToPath(x, y)) {
                qDebug() << "Error: Buildable ground at" << x << "," << y << "is not adjacent to any path tile";
                return false;
            }
        }
    }
    return true;
}

// =========================
//  近战塔阻挡相关的新方法实现
// =========================

bool Map::isBlocked(int x, int y) const {
    return m_blockedTiles.find({x, y}) != m_blockedTiles.end();
}

void Map::setBlocked(int x, int y, bool blocked) const {
    QPoint pt(x, y);
    if (blocked) {
        m_blockedTiles.insert(pt);
    } else {
        m_blockedTiles.erase(pt);
    }
}

// === 新增: 将地图保存到文件 ===
bool Map::saveToFile(std::string const& filePath) const {
    QFile file(QString::fromStdString(filePath));
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "Map::saveToFile: Failed to open file for writing:" << QString::fromStdString(filePath);
        return false;
    }

    QTextStream out(&file);

    // 文件头注释
    out << "# Generated Tower Defense Map File\n";
    out << "# Exported by TowerDefense Editor at " << QDateTime::currentDateTime().toString(Qt::ISODate) << "\n\n";

    // Metadata
    out << "[Metadata]\n";
    out << "name = Exported Map\n";
    out << "width = " << m_width << "\n";
    out << "height = " << m_height << "\n";
    out << "tileSize = " << m_tileSize << "\n\n";

    // Tiles
    out << "[Tiles]\n";
    for (int y = 0; y < m_height; ++y) {
        QString row;
        for (int x = 0; x < m_width; ++x) {
            if (static_cast<size_t>(y) < m_tiles.size() && static_cast<size_t>(x) < m_tiles[y].size()) {
                row.append(tileTypeToChar(m_tiles[y][x]));
            } else {
                row.append('W');  // 默认补墙
            }
        }
        out << row << "\n";
    }
    out << "\n";

    // Paths
    out << "[Paths]\n";
    for (size_t p = 0; p < m_paths.size(); ++p) {
        out << "Path:" << p << "\n";
        for (auto const& pt : m_paths[p]) {
            out << static_cast<int>(pt.x()) << "," << static_cast<int>(pt.y()) << "\n";
        }
        out << "\n";
    }

    file.close();
    qDebug() << "Map saved to" << QString::fromStdString(filePath);
    return true;
}