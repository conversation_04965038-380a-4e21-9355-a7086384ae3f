#include "TowerDefense/GameLogic/UnitFactory.hpp"

#include "TowerDefense/Core/GameData.hpp"
#include "TowerDefense/GameLogic/Enemy.hpp"
#include "TowerDefense/GameLogic/Map.hpp"
#include "TowerDefense/GameLogic/Tower.hpp"

#include <QDebug>

// TowerFactory实现

std::shared_ptr<Tower> TowerFactory::createTower(TowerType type, QPointF const& position, UnitID id) {
    std::shared_ptr<Tower> tower;

    // 获取塔的游戏数据
    try {
        // 获取 GameData 实例
        auto const& gameData = GameData::getInstance();
        auto const& stats    = gameData.getStatsForTower(type);

        switch (type) {
            case TowerType::MELEE:
                tower = std::make_shared<MeleeTower>(id, position);
                break;
            case TowerType::RANGED:
                tower = std::make_shared<RangedArrowTower>(id, position);
                break;
            default:
                qDebug() << "Error: Unknown tower type:" << static_cast<int>(type);
                return nullptr;
        }

        // 记录日志，展示使用的数据
        qDebug() << "Created tower of type" << static_cast<int>(type) << "with ID:" << id << "at position:" << position
                 << "| Cost:" << stats.buildCost << "| HP:" << stats.maxHealth << "| DMG:" << stats.attackDamage
                 << "| Range:" << stats.attackRange;

    } catch (std::exception const& e) {
        qWarning() << "Error loading tower stats: " << e.what();
        return nullptr;
    }

    return tower;
}

// EnemyFactory实现

EnemyFactory::EnemyFactory(Map const& map) : m_map(&map) {
    qDebug() << "EnemyFactory created with reference to Map";
}

std::shared_ptr<Enemy> EnemyFactory::createEnemy(EnemyType type, int pathIndex, UnitID id) {
    std::shared_ptr<Enemy> enemy;

    try {
        // 获取 GameData 实例
        auto const& gameData = GameData::getInstance();
        auto const& stats    = gameData.getStatsForEnemy(type);

        switch (type) {
            case EnemyType::MELEE:
                enemy = std::make_shared<NormalEnemy>(id, *m_map, pathIndex);
                break;
            default:
                qDebug() << "Error: Unknown enemy type:" << static_cast<int>(type);
                return nullptr;
        }

        // 记录日志，展示使用的数据
        qDebug() << "Created enemy of type" << static_cast<int>(type) << "with ID:" << id << "on path" << pathIndex
                 << "| HP:" << stats.maxHealth << "| Speed:" << stats.speed << "| Reward:" << stats.killReward
                 << "| Penalty:" << stats.escapePenalty;

    } catch (std::exception const& e) {
        qWarning() << "Error loading enemy stats: " << e.what();
        return nullptr;
    }

    return enemy;
}
