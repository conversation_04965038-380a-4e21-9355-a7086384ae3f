#include "TowerDefense/Core/GameController.hpp"

#include "TowerDefense/Core/AnimationEventSystem.hpp"
#include "TowerDefense/Core/GameData.hpp"
#include "TowerDefense/Core/ResourceManager.hpp"
#include "TowerDefense/GameLogic/Enemy.hpp"
#include "TowerDefense/GameLogic/Map.hpp"
#include "TowerDefense/GameLogic/Tower.hpp"
#include "TowerDefense/GameLogic/Unit.hpp"
#include "TowerDefense/GameLogic/UnitManager.hpp"
#include "TowerDefense/GameLogic/WaveManager.hpp"

#include <QDebug>
#include <QPoint>
#include <algorithm>
#include <cmath>
#include <memory>
#include <vector>

// 游戏控制器的动画事件观察者
class GameControllerAnimationObserver : public AnimationEventObserver {
   public:
    explicit GameControllerAnimationObserver(GameController* controller) : m_controller(controller) {}

    void onAnimationEvent(AnimationEvent const& event) override {
        if (m_controller != nullptr) {
            m_controller->handleAnimationEvent(event);
        }
    }

   private:
    GameController* m_controller;
};

// 单例模式
GameController& GameController::getInstance() {
    static GameController instance;
    return instance;
}

// 构造函数 - 按照声明顺序初始化成员
GameController::GameController()
    : m_lives(GameData::getInstance().getInitialLives()),  // 从GameData获取默认生命值
      m_currentWave(0),
      m_totalWaves(0),
      m_currentState(GameState::MAIN_MENU),
      m_resourceManager(std::make_unique<ResourceManager>()),
      m_interwaveCountdown(-1.0f),
      m_interwaveCountdownMax(0.0f) {
    qDebug() << "GameController created";

    // 初始化默认金币
    m_resourceManager->setMoney(GameData::getInstance().getInitialMoney());  // 从GameData获取默认金币

    // 注册为动画事件观察者
    registerForAnimationEvents();
}

// 析构函数
GameController::~GameController() {
    // 取消注册动画事件观察者
    unregisterFromAnimationEvents();
    qDebug() << "GameController destroyed";
}

// 注册为动画事件观察者
void GameController::registerForAnimationEvents() {
    m_animationObserver = std::make_shared<GameControllerAnimationObserver>(this);
    AnimationEventSystem::getInstance().registerObserver(m_animationObserver);
    qDebug() << "GameController registered for animation events";
}

// 取消注册动画事件观察者
void GameController::unregisterFromAnimationEvents() {
    if (m_animationObserver) {
        AnimationEventSystem::getInstance().unregisterObserver(m_animationObserver);
        m_animationObserver = nullptr;
        qDebug() << "GameController unregistered from animation events";
    }
}

// 处理动画事件
void GameController::handleAnimationEvent(AnimationEvent const& event) {
    // 根据事件类型进行处理
    switch (event.getType()) {
        case AnimationEventType::TOWER_BUILT:
            // 塔建造动画开始
            qDebug() << "GameController received tower built animation event for tower ID:" << event.getUnitId();

            // 在此处触发UI显示塔的建造进度条
            // TODO: 实现UI进度条，可以是倒计时或建造动画，但不暂停游戏逻辑
            // 例如：notifyTowerBuildProgress(event.getUnitId(), 0.0f); // 开始建造，进度为0

            // 注意：进度条应由定时器或其他机制逐渐更新，直到建造完成
            // 建造完成后应该有一个事件通知系统显示塔已建好
            break;

        case AnimationEventType::TOWER_DESTROYED:
            qDebug() << "GameController received tower destroyed animation event for tower ID:" << event.getUnitId();

            // 在此处触发UI显示塔被摧毁的动画效果
            // TODO: 实现UI动画效果，但不暂停游戏逻辑
            // 例如：notifyTowerDestroyAnimation(event.getUnitId()); // 开始塔被摧毁的动画

            break;

        case AnimationEventType::ENEMY_KILLED:
            qDebug() << "GameController received enemy killed animation event for enemy ID:" << event.getUnitId();
            // 在此处触发UI显示敌人被击杀的动画效果
            // TODO: 实现UI动画效果，但不暂停游戏逻辑
            // 例如：notifyEnemyKilledAnimation(event.getUnitId()); // 开始敌人被击杀的动画
            break;

        case AnimationEventType::ENEMY_ESCAPED:
            qDebug() << "GameController received enemy escaped animation event for enemy ID:" << event.getUnitId();
            // 在此处触发UI显示敌人逃脱的动画效果
            // TODO: 实现UI动画效果，但不暂停游戏逻辑
            // 例如：notifyEnemyEscapedAnimation(event.getUnitId()); // 开始敌人逃脱的动画
            break;

        case AnimationEventType::TOWER_ATTACK:
            qDebug() << "GameController received tower attack animation event from tower ID:" << event.getUnitId()
                     << " to target ID:" << event.getTargetId();
            {
                // 查找攻击者和目标单位指针
                std::shared_ptr<Unit> attackerPtr;
                std::shared_ptr<Unit> targetPtr;
                if (m_unitManager) {
                    attackerPtr = m_unitManager->getUnitById(event.getUnitId());
                    targetPtr   = m_unitManager->getUnitById(event.getTargetId());
                }
                // 发射信号供GUI层播放动画
                emit attackPerformed(attackerPtr ? attackerPtr.get() : nullptr,
                                     targetPtr ? targetPtr.get() : nullptr,
                                     event.getDamage());
            }
            // 在此处触发UI显示塔攻击的动画效果
            // TODO: 实现UI动画效果，但不暂停游戏逻辑
            // 例如：notifyTowerAttackAnimation(event.getUnitId(), event.getTargetId()); // 开始塔攻击的动画
            break;

        case AnimationEventType::ENEMY_DAMAGE_TAKEN:
            qDebug() << "GameController received enemy damage taken animation event for enemy ID:" << event.getUnitId()
                     << ", damage:" << event.getDamage();
            // 在此处触发UI显示敌人受到伤害的动画效果
            // TODO: 实现UI动画效果，但不暂停游戏逻辑
            // 例如：notifyEnemyDamageTakenAnimation(event.getUnitId(), event.getDamage()); // 开始敌人受到伤害的动画
            break;

        default:
            qDebug() << "GameController received unknown animation event type:" << static_cast<int>(event.getType());
            break;
    }

    // 注意：在实际应用中，这里可以使用定时器或其他机制来管理动画的状态和进度
    // 例如定时更新建造进度条，或者播放各种视觉效果
    // 但这些都不应该暂停游戏的主要逻辑，游戏应该继续运行
    // 可以使用Qt的信号槽机制发送动画进度更新事件到UI层
}

// 游戏控制函数

// 开始游戏
void GameController::startGame(std::string const& mapFile) {
    qDebug() << "Starting game with map:" << mapFile.c_str();

    // 加载关卡数据
    loadLevelData(mapFile);

    // 如果地图加载失败，则提前返回并保持在主菜单状态
    if (!m_currentMap) {
        qWarning() << "StartGame aborted: invalid map.";
        updateGameState(GameState::MAIN_MENU);
        return;
    }

    // 重置生命值与金钱
    m_lives = GameData::getInstance().getInitialLives();
    m_resourceManager->setMoney(GameData::getInstance().getInitialMoney());

    // 更新 UI 状态
    updateLives(m_lives);
    updateMoney(static_cast<int>(m_resourceManager->getMoney()));
    updateWave(0);

    updateGameState(GameState::PLAYING);
}

// 暂停游戏
void GameController::pauseGame() {
    if (m_currentState == GameState::PLAYING) {
        updateGameState(GameState::PAUSED);
        qDebug() << "Game paused";
    }
}

// 恢复游戏
void GameController::resumeGame() {
    if (m_currentState == GameState::PAUSED) {
        updateGameState(GameState::PLAYING);
        qDebug() << "Game resumed";
    }
}

// 退出游戏
void GameController::quitGame() {
    updateGameState(GameState::MAIN_MENU);
    qDebug() << "Game quit";
}

// 下一波敌人
void GameController::nextWave() {
    qDebug() << "=== NEXT WAVE CALLED ===";
    if (m_waveManager) {
        qDebug() << "WaveManager exists, calling startNextWave()";
        m_waveManager->startNextWave();
        updateWave(m_waveManager->getCurrentWaveIndex() + 1);
        qDebug() << "Wave updated - Current wave index:" << m_currentWave;
        qDebug() << "WaveManager wave index:" << m_waveManager->getCurrentWaveIndex();
        qDebug() << "Wave in progress:" << m_waveManager->isWaveInProgress();
    } else {
        qWarning() << "WaveManager not initialised - cannot start wave";
    }
    qDebug() << "=== END NEXT WAVE ===";
}

// 放置塔
void GameController::placeTower(int towerType, float x, float y) {
    Q_UNUSED(towerType);
    Q_UNUSED(x);
    Q_UNUSED(y);
    qDebug() << "Place tower requested at" << x << "," << y;
    // TODO: 实现塔放置逻辑
}

// 处理敌人死亡
void GameController::processEnemyDeath(Enemy* enemy) {
    if (enemy == nullptr) {
        qWarning() << "processEnemyDeath called with null enemy pointer";
        return;
    }

    // 向资源管理器增加金币奖励
    int reward = 0;
    try {
        reward = enemy->getRewardCoins();
    } catch (std::exception const& e) {
        qWarning() << "Failed to get enemy reward coins:" << e.what();
    }

    if (reward > 0) {
        addCoins(reward);
        qDebug() << "Enemy" << enemy->getId() << "killed. Rewarded" << reward << "coins.";
    }

    // 发射信号供 UI 层
    emit enemyKilled(enemy);
}

// 处理塔死亡
void GameController::processTowerDeath(Tower* tower) {
    Q_UNUSED(tower);
    qDebug() << "Tower death processed";
    // TODO: 实现塔死亡处理
}

// 扣除生命值
void GameController::loseLives(int amount) {
    updateLives(m_lives - amount);
    qDebug() << "Lost" << amount << "lives, remaining:" << m_lives;

    // 检查失败条件
    if (m_lives <= 0 && m_currentState == GameState::PLAYING) {
        updateGameState(GameState::GAME_OVER_LOSE);
        qDebug() << "Game over: player defeated";
    }
}

// 增加金币
void GameController::addCoins(int amount) {
    if (m_resourceManager) {
        int currentMoney = static_cast<int>(m_resourceManager->getMoney());
        updateMoney(currentMoney + amount);
        qDebug() << "Added" << amount << "coins";
    }
}

// 获取当前状态
GameState GameController::getGameState() const {
    return m_currentState;
}

int GameController::getCurrentMoney() const {
    if (m_resourceManager) {
        return static_cast<int>(m_resourceManager->getMoney());
    }
    return 0;
}

int GameController::getCurrentLives() const {
    return m_lives;
}

int GameController::getCurrentWave() const {
    return m_currentWave;
}

int GameController::getTotalWaves() const {
    return m_totalWaves;
}

void GameController::tick(float deltaTime) {
    updateGame(deltaTime);
}

std::vector<std::shared_ptr<Unit>> GameController::getAllEnemies() const {
    if (m_unitManager) {
        return m_unitManager->getAllEnemies();
    }
    return {};
}

std::vector<std::shared_ptr<Unit>> GameController::getAllTowers() const {
    if (m_unitManager) {
        return m_unitManager->getAllTowers();
    }
    return {};
}

// 私有方法

// 更新游戏逻辑
void GameController::updateGame(float deltaTime) {
    static int updateCount = 0;
    updateCount++;

    // Log every 60 updates (about 2 seconds at 30fps)
    if (updateCount % 60 == 0) {
        qDebug() << "GameController::updateGame called" << updateCount
                 << "times. Current state:" << static_cast<int>(m_currentState);
    }

    if (m_currentState != GameState::PLAYING) {
        if (updateCount % 60 == 0) {
            qDebug() << "Game not in PLAYING state, skipping update";
        }
        return;
    }

    // 更新单位
    if (m_unitManager) {
        m_unitManager->update(deltaTime);
    } else if (updateCount % 60 == 0) {
        qDebug() << "UnitManager is null!";
    }

    // 更新波次管理器
    if (m_waveManager) {
        m_waveManager->update(deltaTime);
    } else if (updateCount % 60 == 0) {
        qDebug() << "WaveManager is null!";
    }

    // 处理波次之间的倒计时
    bool waveInProgress = m_waveManager && m_waveManager->isWaveInProgress();

    // 仅在至少完成过一波之后才自动启动下一波的倒计时
    // 这样玩家在游戏一开始有充足时间进行布防，并且需要手动点击"开始波次"按钮来启动第一波
    if (!waveInProgress && m_interwaveCountdown < 0.0f && m_waveManager && !m_waveManager->isAllWavesCompleted()
        && m_waveManager->getCurrentWaveIndex() >= 0) {
        // 启动新的倒计时
        constexpr float DEFAULT_COUNTDOWN = 20.0f;  // 秒
        m_interwaveCountdown              = DEFAULT_COUNTDOWN;
        m_interwaveCountdownMax           = DEFAULT_COUNTDOWN;
        emit waveCountdownChanged(static_cast<int>(m_interwaveCountdown));
        // 启用开始按钮由 UIAdapter 处理
    }

    // 如果倒计时处于活动状态
    if (m_interwaveCountdown >= 0.0f) {
        m_interwaveCountdown -= deltaTime;

        // 每秒向下取整更新一次UI
        static int lastSecond = -1;
        int currentSec        = static_cast<int>(std::ceil(m_interwaveCountdown));
        if (currentSec != lastSecond) {
            emit waveCountdownChanged(std::max(currentSec, 0));
            lastSecond = currentSec;
        }

        // 倒计时结束，自动开始下一波
        if (m_interwaveCountdown <= 0.0f) {
            m_interwaveCountdown = -1.0f;
            emit waveCountdownChanged(-1);  // 隐藏倒计时
            nextWave();
        }
    }

    // 检查胜利/失败条件
    checkWinLossConditions();
}

// 检查胜利/失败条件
void GameController::checkWinLossConditions() {
    // 失败条件在 loseLives 中已检查

    // 胜利条件：所有波次已完成且场上无敌人
    if (m_waveManager && m_unitManager && m_waveManager->isAllWavesCompleted() && m_unitManager->getAllEnemies().empty()
        && m_currentState == GameState::PLAYING) {
        updateGameState(GameState::GAME_OVER_WIN);
        qDebug() << "Game over: Player won!";
    }
}

// 加载关卡数据（简化示例）
void GameController::loadLevelData(std::string const& mapFile) {
    // 创建并加载地图
    m_currentMap = std::make_shared<Map>();
    if (!m_currentMap->loadFromFile(mapFile)) {
        qWarning() << "Failed to load map from" << mapFile.c_str();
        // 清空无效地图，防止后续逻辑访问
        m_currentMap.reset();
        return;  // 让调用方检测 m_currentMap 是否为空
    }

    // 创建单位管理器与波次管理器
    m_unitManager = std::make_unique<UnitManager>(*m_currentMap, this);
    m_waveManager = std::make_unique<WaveManager>(*m_currentMap, *m_unitManager);

    m_totalWaves = m_waveManager->getTotalWaves();

    qDebug() << "Level data loaded. Total waves:" << m_totalWaves;
}

// 保存游戏
// void GameController::saveGame() {
//     // TODO: 实现游戏存档功能
//     qDebug() << "Game saved (not implemented)";

//     // TODO: 实现UI更新游戏状态
// }

// 加载游戏
// void GameController::loadGame() {
//     // TODO: 实现游戏读档功能
//     qDebug() << "Game loaded (not implemented)";

//     // TODO: 实现UI更新游戏状态
// }

// 鼠标和键盘事件处理

// void GameController::handleMouseClick(int x, int y) {
//     // TODO: 处理鼠标点击事件
//     qDebug() << "Mouse clicked at" << x << "," << y;
// }

// void GameController::handleMouseMove(int x, int y) {
//     // 使用到参数，防止编译器警告
//     (void)x;
//     (void)y;
//     // TODO: 处理鼠标移动事件
//     // 这里一般不需要输出日志，因为移动事件太频繁
// }

// void GameController::handleMouseRelease(int x, int y) {
//     // TODO: 处理鼠标释放事件
//     qDebug() << "Mouse released at" << x << "," << y;
// }

// void GameController::handleKeyPress(int key) {
//     // TODO: 处理键盘按下事件
//     qDebug() << "Key pressed:" << key;
// }

// void GameController::handleKeyRelease(int key) {
//     // TODO: 处理键盘释放事件
//     qDebug() << "Key released:" << key;
// }

// void GameController::handleWindowResize(int width, int height) {
//     // TODO: 处理窗口调整大小事件
//     qDebug() << "Window resized to" << width << "x" << height;

//     // TODO: 实现UI更新游戏状态
// }

// 请求放置塔
bool GameController::requestPlaceTower(TowerType type, QPoint const& gridPos) {
    // 确保有地图可用
    if (!m_currentMap) {
        qWarning() << "GameController::requestPlaceTower: No map is set";
        return false;
    }

    // 检查该位置是否可建造指定类型的塔
    if (!m_currentMap->isBuildable(gridPos.x(), gridPos.y(), type)) {
        qDebug() << "GameController::requestPlaceTower: Position" << gridPos << "is not buildable for tower type"
                 << static_cast<int>(type);
        return false;
    }

    // 检查是否已有塔占用
    auto occupied = std::any_of(m_placedTowers.begin(), m_placedTowers.end(), [&](PlacedTowerInfo const& info) {
        return info.gridPos == gridPos;
    });
    if (occupied) {
        qDebug() << "GameController::requestPlaceTower: Position" << gridPos << "is already occupied";
        return false;
    }

    // 简单的成本系统：近战塔50，远程塔75
    uint32_t cost = (type == TowerType::MELEE) ? 50u : 75u;
    if (!m_resourceManager->subtractMoney(cost)) {
        qDebug() << "GameController::requestPlaceTower: Not enough money (cost=" << cost << ")";
        return false;
    }

    // 调用 UnitManager 实际放置塔（用于战斗与逻辑）
    if (m_unitManager) {
        auto towerUnit = m_unitManager->placeTower(type, gridPos);
        if (!towerUnit) {
            qWarning() << "GameController::requestPlaceTower: UnitManager failed to place tower";
            // 回退已扣除的金币
            m_resourceManager->addMoney(cost);
            return false;
        }
    } else {
        qWarning() << "GameController::requestPlaceTower: UnitManager is null";
        return false;
    }

    // 记录塔信息（仅用于GUI渲染）
    m_placedTowers.push_back({type, gridPos});

    // 发送信号更新金钱
    updateMoney(static_cast<int>(m_resourceManager->getMoney()));

    // 发送塔放置信号（目前不传递实际塔指针）
    emit towerPlaced(nullptr);

    qDebug() << "GameController::requestPlaceTower: Tower placed at" << gridPos;
    return true;
}

// 设置当前地图
void GameController::setMap(std::shared_ptr<Map> map) {
    m_currentMap = std::move(map);
    if (m_currentMap) {
        // 初始化 UnitManager
        m_unitManager = std::make_unique<UnitManager>(*m_currentMap, this);

        // 初始化 WaveManager
        m_waveManager = std::make_unique<WaveManager>(*m_currentMap, *m_unitManager);
        m_totalWaves  = m_waveManager->getTotalWaves();

        // 重置波次进度
        m_currentWave = 0;
        emit waveChanged(m_currentWave, m_totalWaves);
    }
}

// 获取当前地图
std::shared_ptr<Map> GameController::getCurrentMap() const {
    return m_currentMap;
}

// 获取已放置塔列表
std::vector<PlacedTowerInfo> const& GameController::getPlacedTowers() const {
    return m_placedTowers;
}

// 槽函数实现
void GameController::onStartWaveRequested() {
    qDebug() << "=== START WAVE BUTTON CLICKED ===";
    qDebug() << "Current game state:" << static_cast<int>(m_currentState);
    qDebug() << "Current wave index:" << m_currentWave;
    qDebug() << "WaveManager exists:" << (m_waveManager != nullptr);

    if (m_waveManager) {
        qDebug() << "WaveManager current wave index:" << m_waveManager->getCurrentWaveIndex();
        qDebug() << "Wave in progress:" << m_waveManager->isWaveInProgress();
        qDebug() << "All waves completed:" << m_waveManager->isAllWavesCompleted();
    }

    // 如果倒计时仍在进行，给予玩家奖励
    if (m_interwaveCountdown > 0.0f) {
        constexpr int COIN_PER_SEC = 2;
        int rewardCoins            = static_cast<int>(std::ceil(m_interwaveCountdown)) * COIN_PER_SEC;
        addCoins(rewardCoins);
        qDebug() << "Player started wave early, rewarded" << rewardCoins << "coins";
    }

    // 取消倒计时
    m_interwaveCountdown = -1.0f;
    emit waveCountdownChanged(-1);

    // 开始下一波
    qDebug() << "Calling nextWave()...";
    nextWave();
    qDebug() << "=== END START WAVE REQUEST ===";
}

void GameController::onPauseToggled() {
    if (m_currentState == GameState::PLAYING) {
        m_currentState = GameState::PAUSED;
        emit gameStateChanged(m_currentState);
        qDebug() << "Game paused";
    } else if (m_currentState == GameState::PAUSED) {
        m_currentState = GameState::PLAYING;
        emit gameStateChanged(m_currentState);
        qDebug() << "Game resumed";
    }
}

void GameController::onTowerPlaceRequested(TowerType type, QPoint gridPos) {
    requestPlaceTower(type, gridPos);
}

// 内部状态更新方法的实现
void GameController::updateMoney(int newAmount) {
    if (m_resourceManager) {
        int oldAmount = static_cast<int>(m_resourceManager->getMoney());
        int change    = newAmount - oldAmount;
        m_resourceManager->setMoney(static_cast<uint32_t>(newAmount));
        emit moneyChanged(newAmount, change);
    }
}

void GameController::updateLives(int newLives) {
    int change = newLives - m_lives;
    m_lives    = newLives;
    emit livesChanged(m_lives, change);
}

void GameController::updateWave(int newWave) {
    m_currentWave = newWave;
    emit waveChanged(m_currentWave, m_totalWaves);
}

void GameController::updateGameState(GameState newState) {
    if (m_currentState != newState) {
        m_currentState = newState;
        emit gameStateChanged(m_currentState);
    }
}

// Qt MOC will be handled automatically by CMake
