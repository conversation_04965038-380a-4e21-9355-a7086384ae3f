#include "TowerDefense/Core/GameData.hpp"

#include <QDebug>
#include <stdexcept>

// 单例实现
GameData const& GameData::getInstance() {
    static GameData instance;
    return instance;
}

// 构造函数：初始化所有单位的平衡数据
GameData::GameData() : m_initialLives(20), m_initialMoney(500) {
    qDebug() << "Initializing GameData...";

    // 初始化塔的统计数据
    m_towerStats[TowerType::MELEE] = {
        .buildCost      = 100,    // 建造花费
        .sellValue      = 50,     // 出售价值
        .killReward     = 0,      // 杀敌奖励（塔没有）
        .escapePenalty  = 0,      // 逃脱惩罚（塔没有）
        .maxHealth      = 200,    // 生命值
        .speed          = 0.0F,   // 速度（塔不移动）
        .attackDamage   = 30,     // 攻击力
        .attackRange    = 64.0F,  // 攻击范围（1格）
        .attackInterval = 1.0F    // 攻击间隔
    };

    m_towerStats[TowerType::RANGED] = {
        .buildCost      = 150,     // 建造花费
        .sellValue      = 75,      // 出售价值
        .killReward     = 0,       // 杀敌奖励（塔没有）
        .escapePenalty  = 0,       // 逃脱惩罚（塔没有）
        .maxHealth      = 150,     // 生命值
        .speed          = 0.0F,    // 速度（塔不移动）
        .attackDamage   = 20,      // 攻击力
        .attackRange    = 192.0F,  // 攻击范围（3格）
        .attackInterval = 1.5F     // 攻击间隔
    };

    // 初始化塔的视觉数据
    m_towerVisualData[TowerType::MELEE] = {
        .baseTileId         = 182,    // 塔基
        .turretTileId       = 203,    // 塔台（近战塔用）
        .turretEmptyTileId  = 0,      // 无弹药塔台（近战塔不需要）
        .turretLoadedTileId = 0,      // 装弹药塔台（近战塔不需要）
        .projectileTileId   = 297,    // 近战攻击特效
        .hasAmmoSystem      = false,  // 无弹药系统
        .rotationSupport    = true    // 支持旋转
    };

    m_towerVisualData[TowerType::RANGED] = {
        .baseTileId         = 180,   // 塔基
        .turretTileId       = 0,     // 塔台（远程塔不用这个字段）
        .turretEmptyTileId  = 229,   // 无弹药塔台
        .turretLoadedTileId = 206,   // 装弹药塔台
        .projectileTileId   = 252,   // 火箭弹/箭矢
        .hasAmmoSystem      = true,  // 有弹药系统
        .rotationSupport    = true   // 支持旋转
    };

    // 初始化敌人的统计数据
    m_enemyStats[EnemyType::MELEE] = {
        .buildCost      = 0,      // 建造花费（敌人没有）
        .sellValue      = 0,      // 出售价值（敌人没有）
        .killReward     = 30,     // 杀敌奖励
        .escapePenalty  = 1,      // 逃脱惩罚
        .maxHealth      = 100,    // 生命值
        .speed          = 32.0F,   // 速度（0.5格/秒）
        .attackDamage   = 10,     // 攻击力
        .attackRange    = 0.5F,   // 攻击范围
        .attackInterval = 1.0F    // 攻击间隔
    };

    // 初始化敌人的视觉数据
    m_enemyVisualData[EnemyType::MELEE] = {
        // 不同词缀组合的敌人贴图
        .normalTileId        = 245,  // 普通敌人（无词缀）
        .swiftTileId         = 246,  // 神速敌人（SWIFT词缀）
        .blinkingTileId      = 247,  // 闪现敌人（BLINKING词缀）
        .swiftBlinkingTileId = 248,  // 神速+闪现敌人（SWIFT + BLINKING词缀）

        // 攻击效果贴图
        .attackEffectTileId = 295  // 敌人攻击效果贴图
    };

    qDebug() << "GameData initialized.";
}

// 根据塔类型获取其统计数据
UnitBalanceStats const& GameData::getStatsForTower(TowerType type) const {
    auto it = m_towerStats.find(type);
    if (it != m_towerStats.end()) {
        return it->second;
    }
    // 如果找不到对应类型的塔数据，抛出异常或返回默认/错误值
    qWarning() << "GameData: Stats not found for TowerType:" << static_cast<int>(type);
    throw std::runtime_error("Stats not found for TowerType");
}

// 根据敌人类型获取其统计数据
UnitBalanceStats const& GameData::getStatsForEnemy(EnemyType type) const {
    auto it = m_enemyStats.find(type);
    if (it != m_enemyStats.end()) {
        return it->second;
    }
    // 如果找不到对应类型的敌人数据，抛出异常或返回默认/错误值
    qWarning() << "GameData: Stats not found for EnemyType:" << static_cast<int>(type);
    throw std::runtime_error("Stats not found for EnemyType");
}

// 根据塔类型获取其视觉数据
TowerVisualData const& GameData::getVisualDataForTower(TowerType type) const {
    auto it = m_towerVisualData.find(type);
    if (it != m_towerVisualData.end()) {
        return it->second;
    }
    qWarning() << "GameData: Visual data not found for TowerType:" << static_cast<int>(type);
    throw std::runtime_error("Visual data not found for TowerType");
}

// 根据敌人类型获取其视觉数据
EnemyVisualData const& GameData::getVisualDataForEnemy(EnemyType type) const {
    auto it = m_enemyVisualData.find(type);
    if (it != m_enemyVisualData.end()) {
        return it->second;
    }
    qWarning() << "GameData: Visual data not found for EnemyType:" << static_cast<int>(type);
    throw std::runtime_error("Visual data not found for EnemyType");
}

// 根据敌人的词缀组合获取对应的贴图ID
int GameData::getEnemyTileIdByAffixes(EnemyType type, bool hasSwift, bool hasBlinking) const {
    auto it = m_enemyVisualData.find(type);
    if (it == m_enemyVisualData.end()) {
        qWarning() << "GameData: Visual data not found for EnemyType:" << static_cast<int>(type);
        throw std::runtime_error("Visual data not found for EnemyType");
    }

    EnemyVisualData const& visualData = it->second;

    // 根据词缀组合返回对应的贴图ID
    if (hasSwift && hasBlinking) {
        return visualData.swiftBlinkingTileId;  // 神速+闪现
    } else if (hasSwift) {
        return visualData.swiftTileId;  // 仅神速
    } else if (hasBlinking) {
        return visualData.blinkingTileId;  // 仅闪现
    } else {
        return visualData.normalTileId;  // 普通（无词缀）
    }
}

// 获取敌人攻击效果贴图ID
int GameData::getEnemyAttackEffectTileId(EnemyType type) const {
    auto it = m_enemyVisualData.find(type);
    if (it == m_enemyVisualData.end()) {
        qWarning() << "GameData: Visual data not found for EnemyType:" << static_cast<int>(type);
        throw std::runtime_error("Visual data not found for EnemyType");
    }

    return it->second.attackEffectTileId;
}
