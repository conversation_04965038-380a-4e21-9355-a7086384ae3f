#include "TowerDefense/Core/ResourceManager.hpp"

std::uint32_t ResourceManager::getMoney() const {
    return m_money;
}

bool ResourceManager::addMoney(std::uint32_t amount) {
    m_money += amount;
    return true;
}

bool ResourceManager::subtractMoney(std::uint32_t amount) {
    if (m_money >= amount) {
        m_money -= amount;
        return true;
    }
    return false;
}

void ResourceManager::setMoney(std::uint32_t amount) {
    m_money = amount;
}
