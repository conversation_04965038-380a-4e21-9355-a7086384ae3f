#include "TowerDefense/Core/AnimationEventSystem.hpp"

#include <QDebug>
#include <QPointF>
#include <QtCore>

#include <boost/range/algorithm/find.hpp>  // Boost Range find 算法

// 单例实现
AnimationEventSystem& AnimationEventSystem::getInstance() {
    static AnimationEventSystem instance;
    return instance;
}

// 注册观察者
void AnimationEventSystem::registerObserver(std::shared_ptr<AnimationEventObserver> const& observer) {
    if (observer) {
        // 检查观察者是否已经注册
        auto it = boost::find(m_observers, observer);
        if (it == m_observers.end()) {
            m_observers.push_back(observer);
            qDebug() << "AnimationEventSystem: Observer registered";
        }
    }
}

// 取消注册观察者
void AnimationEventSystem::unregisterObserver(std::shared_ptr<AnimationEventObserver> const& observer) {
    if (observer) {
        auto it = boost::find(m_observers, observer);
        if (it != m_observers.end()) {
            m_observers.erase(it);
            qDebug() << "AnimationEventSystem: Observer unregistered";
        }
    }
}

// 发送事件
void AnimationEventSystem::sendEvent(AnimationEvent const& event) {
    for (auto& observer : m_observers) {
        observer->onAnimationEvent(event);
    }
}

// 通知塔建造
void AnimationEventSystem::notifyTowerBuilt(UnitID towerId, QPointF position, UnitType towerType) {
    AnimationEvent event(AnimationEventType::TOWER_BUILT, towerId, position, towerType);
    sendEvent(event);
    qDebug() << "AnimationEvent: Tower built - ID:" << towerId << "at position:" << position;
}

// 通知塔被摧毁
void AnimationEventSystem::notifyTowerDestroyed(UnitID towerId, QPointF position, UnitType towerType) {
    AnimationEvent event(AnimationEventType::TOWER_DESTROYED, towerId, position, towerType);
    sendEvent(event);
    qDebug() << "AnimationEvent: Tower destroyed - ID:" << towerId << "at position:" << position;
}

// 通知敌人被杀死
void AnimationEventSystem::notifyEnemyKilled(UnitID enemyId, QPointF position, UnitType enemyType) {
    AnimationEvent event(AnimationEventType::ENEMY_KILLED, enemyId, position, enemyType);
    sendEvent(event);
    qDebug() << "AnimationEvent: Enemy killed - ID:" << enemyId << "at position:" << position;
}

// 通知敌人逃跑
void AnimationEventSystem::notifyEnemyEscaped(UnitID enemyId, QPointF position, UnitType enemyType) {
    AnimationEvent event(AnimationEventType::ENEMY_ESCAPED, enemyId, position, enemyType);
    sendEvent(event);
    qDebug() << "AnimationEvent: Enemy escaped - ID:" << enemyId << "at position:" << position;
}

// 通知敌人生成
void AnimationEventSystem::notifyEnemySpawned(UnitID enemyId, QPointF position, UnitType enemyType) {
    AnimationEvent event(AnimationEventType::ENEMY_SPAWNED, enemyId, position, enemyType);
    sendEvent(event);
    qDebug() << "AnimationEvent: Enemy spawned - ID:" << enemyId << "at position:" << position;
}

// 通知塔攻击
void AnimationEventSystem::notifyTowerAttack(
    UnitID towerId, QPointF towerPos, UnitID targetId, QPointF targetPos, int damage, UnitType towerType) {
    AnimationEvent event(AnimationEventType::TOWER_ATTACK, towerId, towerPos, towerType);
    event.setTargetId(targetId);
    event.setTargetPosition(targetPos);
    event.setDamage(damage);
    sendEvent(event);
    qDebug() << "AnimationEvent: Tower attack - ID:" << towerId << "attacking target:" << targetId << "for" << damage
             << "damage";
}

// 通知敌人受到伤害
void AnimationEventSystem::notifyEnemyDamageTaken(
    UnitID enemyId, QPointF position, int damage, int remainingHealth, UnitType enemyType) {
    AnimationEvent event(AnimationEventType::ENEMY_DAMAGE_TAKEN, enemyId, position, enemyType);
    event.setDamage(damage);
    event.setHealth(remainingHealth);
    sendEvent(event);
    qDebug() << "AnimationEvent: Enemy damage taken - ID:" << enemyId << "took" << damage
             << "damage, remaining health:" << remainingHealth;
}
