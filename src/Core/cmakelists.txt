add_library(Core STATIC
    GameController.cpp
    AnimationEventSystem.cpp
    ResourceManager.cpp
    GameData.cpp
    ../../include/TowerDefense/Core/GameController.hpp
    ../../include/TowerDefense/Core/AnimationEventSystem.hpp
    ../../include/TowerDefense/Core/ResourceManager.hpp
    ../../include/TowerDefense/Core/GameData.hpp
)

target_include_directories(Core PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../../include>
    $<INSTALL_INTERFACE:include>
)

find_package(Qt6 REQUIRED COMPONENTS Core)
find_package(Boost REQUIRED)
target_link_libraries(Core PRIVATE Qt6::Core ${Boost_LIBRARIES})
target_include_directories(Core PRIVATE ${Boost_INCLUDE_DIRS})

# 启用Qt的自动MOC处理
set_target_properties(Core PROPERTIES
    AUTOMOC ON
)