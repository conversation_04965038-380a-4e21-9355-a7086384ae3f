#include "TowerDefense/Gui/UIAdapter.hpp"

#include "TowerDefense/Core/GameController.hpp"
#include "TowerDefense/Gui/AffixManagementWidget.hpp"
#include "TowerDefense/Gui/GameInfoWidget.hpp"
#include "TowerDefense/Gui/GameViewWidget.hpp"
#include "TowerDefense/Gui/MainWindow.hpp"
#include "TowerDefense/Gui/TowerSelectionWidget.hpp"
#include "TowerDefense/Gui/UnitInfoWidget.hpp"

#include <QDebug>

UIAdapter::UIAdapter(GameController* gameController, MainWindow* mainWindow, QObject* parent)
    : QObject(parent),
      m_gameController(gameController),
      m_mainWindow(mainWindow),
      m_gameInfoWidget(nullptr),
      m_towerSelectionWidget(nullptr),
      m_gameViewWidget(nullptr),
      m_unitInfoWidget(nullptr),
      m_affixManagementWidget(nullptr) {
    qDebug() << "UIAdapter created";
}

void UIAdapter::setupConnections() {
    qDebug() << "Setting up UI connections";

    // 获取GUI组件引用
    obtainWidgetReferences();

    // 建立各种连接
    connectControllerToGui();
    connectGuiToController();
    connectGuiComponents();

    // 初始化GUI状态
    initializeGuiState();

    qDebug() << "UI connections setup complete";
}

void UIAdapter::obtainWidgetReferences() {
    // 从MainWindow获取各个组件的引用
    if (!m_mainWindow) {
        qDebug() << "Error: MainWindow is null";
        return;
    }

    m_gameInfoWidget        = m_mainWindow->getGameInfoWidget();
    m_towerSelectionWidget  = m_mainWindow->getTowerSelectionWidget();
    m_gameViewWidget        = m_mainWindow->getGameViewWidget();
    m_unitInfoWidget        = m_mainWindow->getUnitInfoWidget();
    m_affixManagementWidget = m_mainWindow->getAffixManagementWidget();

    // 设置GameController引用到GameViewWidget
    if (m_gameViewWidget && m_gameController) {
        m_gameViewWidget->setGameController(m_gameController);
    }

    qDebug() << "Widget references obtained";
}

void UIAdapter::connectControllerToGui() {
    if (!m_gameController) {
        return;
    }

    // 连接GameController的信号到GUI组件的槽

    // 游戏状态信号
    connect(m_gameController, &GameController::gameStateChanged, this, &UIAdapter::onGameStateChanged);

    // 资源变化信号
    connect(m_gameController, &GameController::moneyChanged, this, &UIAdapter::onMoneyChanged);

    connect(m_gameController, &GameController::livesChanged, this, &UIAdapter::onLivesChanged);

    connect(m_gameController, &GameController::waveChanged, this, &UIAdapter::onWaveChanged);

    connect(m_gameController, &GameController::waveCountdownChanged, this, &UIAdapter::onWaveCountdownChanged);

    // 战斗效果信号
    if (m_gameViewWidget) {
        connect(
            m_gameController, &GameController::attackPerformed, m_gameViewWidget, &GameViewWidget::onAttackPerformed);
    }

    qDebug() << "Controller to GUI connections established";
}

void UIAdapter::connectGuiToController() {
    if (!m_gameController) {
        return;
    }

    // 连接GUI组件的信号到GameController的槽

    // 塔选择信号
    if (m_towerSelectionWidget) {
        connect(m_towerSelectionWidget,
                &TowerSelectionWidget::towerTypeSelected,
                this,
                &UIAdapter::onTowerSelectionRequested);
    }

    // 塔放置信号
    if (m_gameViewWidget) {
        connect(m_gameViewWidget, &GameViewWidget::placeTowerRequested, this, &UIAdapter::onTowerPlacementRequested);
    }

    // 开始波次按钮
    if (m_gameInfoWidget) {
        connect(m_gameInfoWidget, &GameInfoWidget::startWaveClicked, this, &UIAdapter::onStartWaveButtonClicked);
    }

    qDebug() << "GUI to Controller connections established";
}

void UIAdapter::connectGuiComponents() {
    // 连接GUI组件之间的信号槽

    // 塔选择 -> 游戏视图
    if (m_towerSelectionWidget && m_gameViewWidget) {
        connect(m_towerSelectionWidget,
                &TowerSelectionWidget::towerTypeSelected,
                m_gameViewWidget,
                &GameViewWidget::setTowerPlacementMode);
    }

    // 游戏视图 -> 单位信息
    if (m_gameViewWidget && m_unitInfoWidget) {
        connect(m_gameViewWidget, &GameViewWidget::unitSelected, m_unitInfoWidget, &UnitInfoWidget::showUnitInfo);
    }

    qDebug() << "GUI component connections established";
}

void UIAdapter::initializeGuiState() {
    if (!m_gameController) {
        return;
    }

    // 初始化GUI组件的状态
    if (m_gameInfoWidget) {
        m_gameInfoWidget->updateMoney(m_gameController->getCurrentMoney());
        m_gameInfoWidget->updateLives(m_gameController->getCurrentLives());
        m_gameInfoWidget->updateWaveProgress(m_gameController->getCurrentWave(), m_gameController->getTotalWaves());
        m_gameInfoWidget->setStartWaveButtonEnabled(true);
    }

    if (m_towerSelectionWidget) {
        m_towerSelectionWidget->updateAvailableMoney(m_gameController->getCurrentMoney());
    }

    qDebug() << "GUI state initialized";
}

// 槽函数实现

void UIAdapter::onGameStateChanged(GameState newState) {
    qDebug() << "Game state changed to:" << static_cast<int>(newState);

    // 根据游戏状态更新GUI
    // TODO: 实现具体的状态更新逻辑
}

void UIAdapter::onMoneyChanged(int newAmount, int change) {
    qDebug() << "Money changed to:" << newAmount << " (change:" << change << ")";

    // 更新相关GUI组件
    if (m_gameInfoWidget) {
        m_gameInfoWidget->updateMoney(newAmount);
    }

    if (m_towerSelectionWidget) {
        m_towerSelectionWidget->updateAvailableMoney(newAmount);
    }
}

void UIAdapter::onLivesChanged(int newLives, int change) {
    qDebug() << "Lives changed to:" << newLives << " (change:" << change << ")";

    if (m_gameInfoWidget) {
        m_gameInfoWidget->updateLives(newLives);
    }
}

void UIAdapter::onWaveChanged(int currentWave, int totalWaves) {
    qDebug() << "Wave changed to:" << currentWave << "/" << totalWaves;

    if (m_gameInfoWidget) {
        m_gameInfoWidget->updateWaveProgress(currentWave, totalWaves);
    }
}

void UIAdapter::onWaveCountdownChanged(int secondsRemaining) {
    if (m_gameInfoWidget) {
        m_gameInfoWidget->updateCountdown(secondsRemaining);

        // Enable start wave button only when countdown active and >0
        bool enableButton = secondsRemaining >= 0;
        m_gameInfoWidget->setStartWaveButtonEnabled(enableButton);
    }
}

void UIAdapter::onStartWaveButtonClicked() {
    if (m_gameController) {
        m_gameController->onStartWaveRequested();
    }
}

void UIAdapter::onTowerSelectionRequested(TowerType type) {
    qDebug() << "Tower selection requested, type:" << static_cast<int>(type);

    // 通知GameViewWidget进入塔放置模式
    if (m_gameViewWidget) {
        m_gameViewWidget->setTowerPlacementMode(type);
    }
}

void UIAdapter::onTowerPlacementRequested(TowerType type, QPoint const& gridPos) {
    qDebug() << "Tower placement requested, type:" << static_cast<int>(type) << " at position:" << gridPos;

    // 转发给GameController
    if (m_gameController) {
        bool success = m_gameController->requestPlaceTower(type, gridPos);

        if (success) {
            qDebug() << "Tower placed successfully";
            // 退出放置模式
            if (m_gameViewWidget) {
                m_gameViewWidget->clearTowerPlacementMode();
            }
        } else {
            qDebug() << "Tower placement failed";
            // 可以在这里显示错误信息
        }
    }
}