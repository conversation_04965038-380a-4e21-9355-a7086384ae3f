#include "TowerDefense/Gui/AffixManagementWidget.hpp"

// TODO: 需要包含实际的Tower类定义
// #include "TowerDefense/GameLogic/Tower.hpp"

AffixManagementWidget::AffixManagementWidget(QWidget* parent) : QWidget(parent) {
    setupUI();
}

void AffixManagementWidget::setupUI() {
    // 创建主布局
    auto* mainLayout = new QVBoxLayout(this);

    // 创建分组框
    m_groupBox = new QGroupBox("词缀管理", this);

    // 创建按钮
    m_showLibraryButton = new QPushButton("显示词缀库", this);
    m_equipAffixButton  = new QPushButton("装备词缀", this);

    // 创建词缀槽显示
    m_slot1Label = new QLabel("槽位1: 空", this);
    m_slot2Label = new QLabel("槽位2: 空", this);

    // 创建卸下按钮
    m_unequipSlot1Button = new QPushButton("卸下", this);
    m_unequipSlot2Button = new QPushButton("卸下", this);

    // 设置样式
    m_slot1Label->setStyleSheet("font-weight: bold; border: 1px solid gray; padding: 5px;");
    m_slot2Label->setStyleSheet("font-weight: bold; border: 1px solid gray; padding: 5px;");

    // 连接信号槽
    connect(m_showLibraryButton, &QPushButton::clicked, this, &AffixManagementWidget::onShowLibraryClicked);
    connect(m_equipAffixButton, &QPushButton::clicked, this, &AffixManagementWidget::onEquipAffixClicked);
    connect(m_unequipSlot1Button, &QPushButton::clicked, this, &AffixManagementWidget::onUnequipSlot1Clicked);
    connect(m_unequipSlot2Button, &QPushButton::clicked, this, &AffixManagementWidget::onUnequipSlot2Clicked);

    // 创建分组框布局
    auto* groupLayout = new QVBoxLayout(m_groupBox);

    // 添加控制按钮
    groupLayout->addWidget(m_showLibraryButton);
    groupLayout->addWidget(m_equipAffixButton);

    groupLayout->addSpacing(10);

    // 添加槽位1
    auto* slot1Layout = new QHBoxLayout();
    slot1Layout->addWidget(m_slot1Label);
    slot1Layout->addWidget(m_unequipSlot1Button);
    groupLayout->addLayout(slot1Layout);

    // 添加槽位2
    auto* slot2Layout = new QHBoxLayout();
    slot2Layout->addWidget(m_slot2Label);
    slot2Layout->addWidget(m_unequipSlot2Button);
    groupLayout->addLayout(slot2Layout);

    // 添加到主布局
    mainLayout->addWidget(m_groupBox);
    mainLayout->addStretch();

    setLayout(mainLayout);

    // 初始化显示
    updateDisplay();
}

void AffixManagementWidget::setSelectedTower(Tower* tower) {
    m_selectedTower = tower;
    updateDisplay();
}

void AffixManagementWidget::updateAffixLibrary(std::vector<TowerAffix> const& availableAffixes) {
    m_availableAffixes = availableAffixes;
    updateDisplay();
}

void AffixManagementWidget::updateDisplay() {
    // 如果没有选中的塔，禁用所有控件
    bool hasTower = (m_selectedTower != nullptr);

    m_equipAffixButton->setEnabled(hasTower && !m_availableAffixes.empty());
    m_unequipSlot1Button->setEnabled(hasTower);
    m_unequipSlot2Button->setEnabled(hasTower);

    if (!hasTower) {
        m_slot1Label->setText("槽位1: 无选中塔");
        m_slot2Label->setText("槽位2: 无选中塔");
        return;
    }

    // TODO: 实际实现需要从Tower对象获取词缀信息
    // 目前使用占位符
    m_slot1Label->setText("槽位1: 空");
    m_slot2Label->setText("槽位2: 空");

    // auto affixes = m_selectedTower->getAffixes();
    // m_slot1Label->setText(QString("槽位1: %1").arg(
    //     affixes.size() > 0 ? getAffixName(affixes[0]) : "空"));
    // m_slot2Label->setText(QString("槽位2: %1").arg(
    //     affixes.size() > 1 ? getAffixName(affixes[1]) : "空"));
}

QString AffixManagementWidget::getAffixName(TowerAffix affix) const {
    switch (affix) {
        case TowerAffix::NONE:
            return "无";
        case TowerAffix::BERSERK:
            return "狂暴的";
        case TowerAffix::ICE:
            return "冰系的";
        case TowerAffix::AOE_SPLASH:
            return "群伤的";
        case TowerAffix::BLEEDING:
            return "放血的";
        default:
            return "未知";
    }
}

void AffixManagementWidget::onShowLibraryClicked() {
    emit showAffixLibraryRequested();
}

void AffixManagementWidget::onEquipAffixClicked() {
    if (!m_selectedTower || m_availableAffixes.empty()) {
        return;
    }

    // TODO: 实现词缀选择对话框或菜单
    // 目前只是发出信号，实际需要让用户选择词缀和槽位

    // 简化：装备第一个可用词缀到第一个槽位
    if (!m_availableAffixes.empty()) {
        emit affixEquipRequested(m_selectedTower, m_availableAffixes[0], 0);
    }
}

void AffixManagementWidget::onUnequipSlot1Clicked() {
    if (m_selectedTower) {
        emit affixUnequipRequested(m_selectedTower, 0);
    }
}

void AffixManagementWidget::onUnequipSlot2Clicked() {
    if (m_selectedTower) {
        emit affixUnequipRequested(m_selectedTower, 1);
    }
}