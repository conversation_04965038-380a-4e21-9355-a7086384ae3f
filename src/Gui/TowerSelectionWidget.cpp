#include "TowerDefense/Gui/TowerSelectionWidget.hpp"

#include <QKeyEvent>

TowerSelectionWidget::TowerSelectionWidget(QWidget* parent) : QWidget(parent) {
    setupUI();
}

void TowerSelectionWidget::setupUI() {
    // 创建主布局
    auto* mainLayout = new QVBoxLayout(this);

    // 创建分组框
    m_groupBox = new QGroupBox("塔选择", this);

    // 创建按钮
    m_meleeTowerButton  = new QPushButton("近战塔", this);
    m_rangedTowerButton = new QPushButton("远程塔", this);

    // 创建成本标签
    m_meleeCostLabel  = new QLabel(QString("成本: %1").arg(MELEE_TOWER_COST), this);
    m_rangedCostLabel = new QLabel(QString("成本: %1").arg(RANGED_TOWER_COST), this);

    // 设置按钮样式
    m_meleeTowerButton->setMinimumHeight(40);
    m_rangedTowerButton->setMinimumHeight(40);

    // 连接信号槽
    connect(m_meleeTowerButton, &QPushButton::clicked, this, &TowerSelectionWidget::onMeleeTowerClicked);
    connect(m_rangedTowerButton, &QPushButton::clicked, this, &TowerSelectionWidget::onRangedTowerClicked);

    // 创建分组框布局
    auto* groupLayout = new QVBoxLayout(m_groupBox);

    // 添加近战塔
    groupLayout->addWidget(m_meleeTowerButton);
    groupLayout->addWidget(m_meleeCostLabel);

    // 添加分隔
    groupLayout->addSpacing(10);

    // 添加远程塔
    groupLayout->addWidget(m_rangedTowerButton);
    groupLayout->addWidget(m_rangedCostLabel);

    // 添加到主布局
    mainLayout->addWidget(m_groupBox);
    mainLayout->addStretch();

    setLayout(mainLayout);

    // 初始化按钮状态
    updateButtonStates();
}

void TowerSelectionWidget::updateAvailableMoney(int money) {
    m_currentMoney = money;
    updateButtonStates();
}

void TowerSelectionWidget::updateButtonStates() {
    // 根据金钱数量启用/禁用按钮
    m_meleeTowerButton->setEnabled(m_currentMoney >= MELEE_TOWER_COST);
    m_rangedTowerButton->setEnabled(m_currentMoney >= RANGED_TOWER_COST);

    // 更新按钮样式
    if (m_currentMoney >= MELEE_TOWER_COST) {
        m_meleeTowerButton->setStyleSheet("QPushButton { background-color: lightgreen; }");
    } else {
        m_meleeTowerButton->setStyleSheet("QPushButton { background-color: lightgray; }");
    }

    if (m_currentMoney >= RANGED_TOWER_COST) {
        m_rangedTowerButton->setStyleSheet("QPushButton { background-color: lightgreen; }");
    } else {
        m_rangedTowerButton->setStyleSheet("QPushButton { background-color: lightgray; }");
    }
}

void TowerSelectionWidget::onMeleeTowerClicked() {
    if (m_currentMoney >= MELEE_TOWER_COST) {
        emit towerTypeSelected(TowerType::MELEE);
    }
}

void TowerSelectionWidget::onRangedTowerClicked() {
    if (m_currentMoney >= RANGED_TOWER_COST) {
        emit towerTypeSelected(TowerType::RANGED);
    }
}

void TowerSelectionWidget::keyPressEvent(QKeyEvent* event) {
    // 快捷键支持
    switch (event->key()) {
        case Qt::Key_1:
            if (m_meleeTowerButton->isEnabled()) {
                onMeleeTowerClicked();
            }
            break;
        case Qt::Key_2:
            if (m_rangedTowerButton->isEnabled()) {
                onRangedTowerClicked();
            }
            break;
        default:
            QWidget::keyPressEvent(event);
            break;
    }
}