#include "TowerDefense/Gui/GameViewWidget.hpp"

#include "TowerDefense/Core/GameController.hpp"
#include "TowerDefense/Core/GameData.hpp"
#include "TowerDefense/GameLogic/Enemy.hpp"
#include "TowerDefense/GameLogic/Map.hpp"
#include "TowerDefense/GameLogic/Tower.hpp"
#include "TowerDefense/GameLogic/Unit.hpp"
#include "TowerDefense/Types.hpp"
#include "TowerDefense/Utils/TileRenderer.hpp"
#include "TowerDefense/Utils/TilesheetConfig.hpp"

// TODO: 需要包含实际的游戏逻辑类定义
// #include "TowerDefense/GameLogic/Unit.hpp"
// #include "TowerDefense/GameLogic/Tower.hpp"
// #include "TowerDefense/GameLogic/Enemy.hpp"

#include <QColor>
#include <QDateTime>
#include <QKeyEvent>
#include <QMouseEvent>
#include <QPaintEvent>
#include <algorithm>
#include <cmath>

GameViewWidget::GameViewWidget(QWidget* parent) : QWidget(parent) {
    // 设置焦点策略，使其能够接收键盘事件
    setFocusPolicy(Qt::StrongFocus);

    // 设置最小尺寸
    setMinimumSize(640, 480);

    // 创建更新定时器
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, this, &GameViewWidget::onGameUpdated);
    m_updateTimer->start(16);  // 约60fps，提高视觉流畅度

    // 设置背景颜色
    setStyleSheet("background-color: darkgreen;");

    // 为了避免在快速移动精灵时出现残影，确保每帧都对整个区域进行不透明重绘
    setAttribute(Qt::WA_OpaquePaintEvent);

    // 启用平滑像素变换，减少移动中的锯齿与拖影
    setAttribute(Qt::WA_NoSystemBackground, false);

    // 额外的渲染提示
    setContentsMargins(0, 0, 0, 0);
}

GameViewWidget::~GameViewWidget() = default;

void GameViewWidget::setGameController(GameController* controller) {
    m_gameController = controller;
}

void GameViewWidget::setMap(std::shared_ptr<Map> map) {
    m_map = map;

    // 根据地图尺寸设置widget的固定大小
    if (m_map && m_tileRenderer) {
        int tileSize = m_tileRenderer->getTileSize();
        int width    = m_map->getWidth() * tileSize;
        int height   = m_map->getHeight() * tileSize;
        setFixedSize(width, height);
        qDebug() << "GameViewWidget: Set fixed size to" << width << "x" << height;
    }

    qDebug() << "GameViewWidget: Map set";
}

void GameViewWidget::setTileRenderer(std::shared_ptr<TileRenderer> tileRenderer) {
    m_tileRenderer = tileRenderer;

    // 如果地图也已设置，则更新widget尺寸
    if (m_map && m_tileRenderer) {
        int tileSize = m_tileRenderer->getTileSize();
        int width    = m_map->getWidth() * tileSize;
        int height   = m_map->getHeight() * tileSize;
        setFixedSize(width, height);
        qDebug() << "GameViewWidget: Set fixed size to" << width << "x" << height;
    }

    qDebug() << "GameViewWidget: TileRenderer set";
}

void GameViewWidget::paintEvent(QPaintEvent* event) {
    Q_UNUSED(event);

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setRenderHint(QPainter::SmoothPixmapTransform);

    // 绘制各个层级
    drawBackground(painter);
    drawGrid(painter);
    drawPaths(painter);
    drawUnits(painter);
    drawEffects(painter);
}

void GameViewWidget::drawBackground(QPainter& painter) {
    // 绘制简单的背景
    painter.fillRect(rect(), QColor(34, 139, 34));  // 森林绿背景
}

void GameViewWidget::drawGrid(QPainter& painter) {
    painter.setPen(QPen(QColor(255, 255, 255, 50), 1));

    // 绘制网格线
    int gridWidth  = width() / TILE_SIZE;
    int gridHeight = height() / TILE_SIZE;

    // 垂直线
    for (int x = 0; x <= gridWidth; ++x) {
        int screenX = x * TILE_SIZE;
        painter.drawLine(screenX, 0, screenX, height());
    }

    // 水平线
    for (int y = 0; y <= gridHeight; ++y) {
        int screenY = y * TILE_SIZE;
        painter.drawLine(0, screenY, width(), screenY);
    }
}

void GameViewWidget::drawPaths(QPainter& painter) {
    if (!m_map || !m_tileRenderer || !m_tileRenderer->isReady()) {
        // Fallback to simple drawing if no map or tile renderer
        painter.setPen(QPen(Qt::yellow, 2));
        painter.setBrush(QBrush(QColor(139, 69, 19)));  // 棕色路径

        // 从左到右的简单路径
        for (int x = 2; x < 20; ++x) {
            QPointF screenPos = gridToScreen(QPoint(x, 5));
            painter.drawRect(screenPos.x(), screenPos.y(), TILE_SIZE, TILE_SIZE);
        }

        // 绘制起点（绿色）
        QPointF startPos = gridToScreen(QPoint(2, 5));
        painter.setBrush(QBrush(Qt::green));
        painter.drawRect(startPos.x(), startPos.y(), TILE_SIZE, TILE_SIZE);

        // 绘制终点（红色）
        QPointF endPos = gridToScreen(QPoint(19, 5));
        painter.setBrush(QBrush(Qt::red));
        painter.drawRect(endPos.x(), endPos.y(), TILE_SIZE, TILE_SIZE);
        return;
    }

    // Render actual map tiles using the tile renderer
    int mapWidth       = m_map->getWidth();
    int mapHeight      = m_map->getHeight();
    int actualTileSize = m_tileRenderer->getTileSize();

    // Only log once to avoid spam
    static bool logged = false;
    if (!logged) {
        qDebug() << "GameViewWidget: Rendering map" << mapWidth << "x" << mapHeight << "with tile size"
                 << actualTileSize;
        logged = true;
    }

    for (int y = 0; y < mapHeight; ++y) {
        for (int x = 0; x < mapWidth; ++x) {
            TileType tileType = m_map->getTileType(x, y);
            if (tileType != TileType::INVALID) {
                QPointF screenPos = gridToScreen(QPoint(x, y));
                QRect tileRect(screenPos.x(), screenPos.y(), actualTileSize, actualTileSize);

                if (tileType == TileType::OBSTACLE) {
                    // Special handling for obstacle tiles (layered rendering)
                    m_tileRenderer->renderObstacleTile(painter, tileRect);
                } else {
                    // Regular tile rendering
                    m_tileRenderer->renderMapTile(painter, tileRect, tileType);
                }
            }
        }
    }
}

void GameViewWidget::drawUnits(QPainter& painter) {
    if (!m_gameController || !m_tileRenderer) {
        return;
    }

    int tileSize = m_tileRenderer->getTileSize();

    // 绘制已放置塔（基于实际塔单位，支持旋转与弹药贴图）
    auto towerUnits = m_gameController ? m_gameController->getAllTowers() : std::vector<std::shared_ptr<Unit>>{};

    for (auto const& unitPtr : towerUnits) {
        auto* towerPtr = dynamic_cast<Tower*>(unitPtr.get());
        if (!towerPtr) {
            continue;
        }

        // 获取视觉数据
        auto const& visual = GameData::getInstance().getVisualDataForTower(towerPtr->getTowerType());

        QPointF pos = towerPtr->getPosition();  // 世界坐标（瓦片中心）

        // 绘制塔基
        QRect baseRect(
            static_cast<int>(pos.x() - tileSize / 2), static_cast<int>(pos.y() - tileSize / 2), tileSize, tileSize);
        m_tileRenderer->renderTileById(painter, baseRect, visual.baseTileId);

        // 选择塔台贴图
        int turretTileId = visual.turretTileId;
        if (visual.hasAmmoSystem) {
            turretTileId = towerPtr->isAmmoLoaded() ? visual.turretLoadedTileId : visual.turretEmptyTileId;
        }

        // 绘制塔台（支持旋转）
        painter.save();
        painter.translate(pos);
        // 默认贴图朝向为"上"，但游戏中认为0度指向右侧，因此额外加90度
        painter.rotate(towerPtr->getRotation() + 90.0);

        QRect turretRect(-tileSize / 2, -tileSize / 2, tileSize, tileSize);
        m_tileRenderer->renderTileById(painter, turretRect, turretTileId);
        painter.restore();

        // 绘制血条
        drawSimpleHealthBar(painter, towerPtr);
    }

    // 绘制敌人
    auto enemies = m_gameController->getAllEnemies();
    for (auto const& enemyPtr : enemies) {
        EnemyType eType = EnemyType::MELEE;  // 默认
        if (enemyPtr->getType() == UnitType::ENEMY_MELEE) {
            eType = EnemyType::MELEE;
        }

        // TODO: 根据词缀判断贴图
        int tileId = GameData::getInstance().getEnemyTileIdByAffixes(eType, false, false);

        // Enemy position is stored in world coordinates (pixels), where each tile spans tileSize pixels
        // and Map::gridToWorld stores the center of the tile. Therefore we should draw the enemy
        // centered on that world position without an additional tileSize scaling.
        QPointF pos = enemyPtr->getPosition();  // World coordinates

        // 获取敌人朝向
        float enemyRotation = 0.0f;
        if (auto* enemyObj = dynamic_cast<Enemy*>(enemyPtr.get())) {
            enemyRotation = enemyObj->getRotation();
        }

        painter.save();
        painter.translate(pos);
        // 敌人默认贴图方向朝上（-Y），因此补偿+90°
        painter.rotate(enemyRotation);
        QRect enemyRect(-tileSize / 2, -tileSize / 2, tileSize, tileSize);
        m_tileRenderer->renderTileById(painter, enemyRect, tileId);
        painter.restore();

        // 绘制血条（不需旋转，因此在 restore 之后）
        drawSimpleHealthBar(painter, enemyPtr.get());
    }
}

void GameViewWidget::drawTower(QPainter& painter, Tower const* tower) {
    Q_UNUSED(painter);
    Q_UNUSED(tower);
    // TODO: 实现具体的塔绘制逻辑
    // painter.setBrush(QBrush(Qt::blue));
    // painter.setPen(QPen(Qt::black, 2));
    // QPointF pos = gridToScreen(tower->getPosition());
    // painter.drawEllipse(pos.x() + 2, pos.y() + 2, TILE_SIZE - 4, TILE_SIZE - 4);
}

void GameViewWidget::drawEnemy(QPainter& painter, Enemy const* enemy) {
    Q_UNUSED(painter);
    Q_UNUSED(enemy);
    // TODO: 实现具体的敌人绘制逻辑
    // painter.setBrush(QBrush(Qt::red));
    // painter.setPen(QPen(Qt::black, 1));
    // QPointF pos = gridToScreen(enemy->getPosition());
    // painter.drawEllipse(pos.x() + 4, pos.y() + 4, TILE_SIZE - 8, TILE_SIZE - 8);
}

void GameViewWidget::drawSimpleHealthBar(QPainter& painter, Unit const* unit) {
    if (!unit || !m_tileRenderer) {
        return;
    }

    int tileSize = m_tileRenderer->getTileSize();

    // 获取最大生命值
    int maxHp = 1;
    int curHp = unit->getHealth();

    if (unit->getType() == UnitType::TOWER_MELEE || unit->getType() == UnitType::TOWER_RANGED) {
        auto* towerPtr = dynamic_cast<Tower const*>(unit);
        if (towerPtr) {
            auto const& stats = GameData::getInstance().getStatsForTower(towerPtr->getTowerType());
            maxHp             = stats.maxHealth;
        }
    } else if (unit->getType() == UnitType::ENEMY_MELEE) {
        auto* enemyPtr = dynamic_cast<Enemy const*>(unit);
        if (enemyPtr) {
            auto const& stats = GameData::getInstance().getStatsForEnemy(enemyPtr->getEnemyType());
            maxHp             = stats.maxHealth;
        }
    }

    if (maxHp <= 0) {
        maxHp = 1;
    }

    float ratio = static_cast<float>(curHp) / static_cast<float>(maxHp);
    ratio       = std::clamp(ratio, 0.0f, 1.0f);

    QPointF pos   = unit->getPosition();
    int barWidth  = tileSize - 4;
    int barHeight = 5;

    int left = static_cast<int>(pos.x() - barWidth / 2);
    int top  = static_cast<int>(pos.y() - tileSize / 2 - barHeight - 2);

    // 背景
    painter.setBrush(Qt::darkGray);
    painter.setPen(Qt::NoPen);
    painter.drawRect(left, top, barWidth, barHeight);

    // 前景
    painter.setBrush(ratio > 0.5f ? Qt::green : (ratio > 0.2f ? QColor(255, 165, 0) : Qt::red));
    painter.drawRect(left + 1, top + 1, static_cast<int>((barWidth - 2) * ratio), barHeight - 2);
}

void GameViewWidget::drawAffixIndicators(QPainter& painter, Unit const* unit) {
    Q_UNUSED(painter);
    Q_UNUSED(unit);
    // TODO: 绘制词缀指示器
    // auto affixes = unit->getAffixes();
    // for (size_t i = 0; i < affixes.size(); ++i) {
    //     // 在单位上方绘制词缀图标或文字
    // }
}

void GameViewWidget::drawEffects(QPainter& painter) {
    // 绘制攻击/投射物效果
    for (auto const& effect : m_attackEffects) {
        QPointF currentPos = effect.startPos + effect.progress * (effect.endPos - effect.startPos);

        // 如果提供了投射物贴图，则绘制投射物精灵
        if (effect.projectileTileId != -1 && m_tileRenderer && m_tileRenderer->isReady()) {
            int tileSize = m_tileRenderer->getTileSize();

            // 计算投射物朝向角度
            QPointF dir         = effect.endPos - effect.startPos;
            constexpr double PI = 3.14159265358979323846;
            double angleRad     = std::atan2(dir.y(), dir.x());
            double angleDeg     = angleRad * 180.0 / PI;

            painter.save();
            painter.translate(currentPos);
            // 同样补偿贴图默认朝向: 默认朝上, 0度指向右 -> 加90°
            painter.rotate(angleDeg + 90.0);

            QRect projectileRect(-tileSize / 2, -tileSize / 2, tileSize, tileSize);
            m_tileRenderer->renderTileById(painter, projectileRect, effect.projectileTileId);
            painter.restore();

            // 如果目标将被击杀，则在弹道飞行过程中保留其最后贴图
            if (effect.targetWillDie && effect.targetTileId != -1) {
                QRect enemyRect(static_cast<int>(effect.targetLastPos.x() - tileSize / 2),
                                static_cast<int>(effect.targetLastPos.y() - tileSize / 2),
                                tileSize,
                                tileSize);
                m_tileRenderer->renderTileById(painter, enemyRect, effect.targetTileId);
            }
        } else {
#ifndef NDEBUG
// debug output maybe later
#endif
            painter.setPen(QPen(effect.color, 3));
            painter.drawLine(effect.startPos, currentPos);

            if (effect.targetWillDie && effect.targetTileId != -1) {
                int tileSize = m_tileRenderer ? m_tileRenderer->getTileSize() : TILE_SIZE;
                QRect enemyRect(static_cast<int>(effect.targetLastPos.x() - tileSize / 2),
                                static_cast<int>(effect.targetLastPos.y() - tileSize / 2),
                                tileSize,
                                tileSize);
                if (m_tileRenderer && m_tileRenderer->isReady()) {
                    m_tileRenderer->renderTileById(painter, enemyRect, effect.targetTileId);
                }
            }
        }
    }

    // 绘制闪烁效果
    painter.setBrush(QBrush(QColor(255, 255, 0, 100)));
    for (auto const& flash : m_flashPositions) {
        int alpha = flash.remainingFrames * 5;  // 逐渐消失
        painter.setBrush(QBrush(QColor(255, 255, 0, alpha)));
        painter.drawEllipse(flash.position.x() - 5, flash.position.y() - 5, 10, 10);
    }
}

void GameViewWidget::mousePressEvent(QMouseEvent* event) {
    QPoint gridPos = screenToGrid(event->position());

    emit tileClicked(gridPos, event->button());

    if (event->button() == Qt::LeftButton) {
        // 左键点击处理
        if (m_placingTowerType.has_value()) {
            // 正在放置塔模式
            if (canPlaceTowerAt(gridPos, m_placingTowerType.value())) {
                emit placeTowerRequested(m_placingTowerType.value(), gridPos);
                clearTowerPlacementMode();
            }
        } else {
            // 选择单位模式
            Unit* unit = findUnitAt(gridPos);
            if (unit != m_selectedUnit) {
                m_selectedUnit = unit;
                emit unitSelected(unit);
            }
        }
    } else if (event->button() == Qt::RightButton) {
        // 右键点击 - 弹出塔选择菜单或取消当前操作
        if (m_placingTowerType.has_value()) {
            // 如果正在放置塔，取消放置模式
            clearTowerPlacementMode();
        } else {
            // 显示塔选择菜单
            showTowerSelectionMenu(event->globalPosition().toPoint(), gridPos);
        }

        // 清除单位选择
        m_selectedUnit = nullptr;
        emit unitSelected(nullptr);
    }

    update();  // 重绘
}

void GameViewWidget::keyPressEvent(QKeyEvent* event) {
    switch (event->key()) {
        case Qt::Key_Escape:
            clearTowerPlacementMode();
            m_selectedUnit = nullptr;
            emit unitSelected(nullptr);
            update();
            break;
        case Qt::Key_1:
            setTowerPlacementMode(TowerType::MELEE);
            break;
        case Qt::Key_2:
            setTowerPlacementMode(TowerType::RANGED);
            break;
        default:
            QWidget::keyPressEvent(event);
            break;
    }
}

QPoint GameViewWidget::screenToGrid(QPointF const& screenPos) const {
    int tileSize = m_tileRenderer ? m_tileRenderer->getTileSize() : TILE_SIZE;
    return QPoint(static_cast<int>(screenPos.x()) / tileSize, static_cast<int>(screenPos.y()) / tileSize);
}

QPointF GameViewWidget::gridToScreen(QPoint const& gridPos) const {
    int tileSize = m_tileRenderer ? m_tileRenderer->getTileSize() : TILE_SIZE;
    return QPointF(gridPos.x() * tileSize, gridPos.y() * tileSize);
}

Unit* GameViewWidget::findUnitAt(QPoint const& gridPos) const {
    Q_UNUSED(gridPos);
    // TODO: 从GameController获取指定位置的单位
    return nullptr;
}

bool GameViewWidget::canPlaceTowerAt(QPoint const& gridPos, TowerType type) const {
    if (!m_map) {
        return false;
    }

    // 检查是否在地图范围内
    if (gridPos.x() < 0 || gridPos.x() >= m_map->getWidth() || gridPos.y() < 0 || gridPos.y() >= m_map->getHeight()) {
        return false;
    }

    // 获取该位置的地形类型
    TileType tileType = m_map->getTileType(gridPos.x(), gridPos.y());

    // 根据塔类型和地形类型判断是否可以放置
    switch (type) {
        case TowerType::MELEE:
            // 近战塔可以放在路径上(PATH, START_POINT, END_POINT)
            return tileType == TileType::PATH || tileType == TileType::START_POINT || tileType == TileType::END_POINT;

        case TowerType::RANGED:
            // 远程塔可以放在可建造地面(BUILDABLE_GROUND)
            return tileType == TileType::BUILDABLE_GROUND;

        default:
            return false;
    }
}

void GameViewWidget::onGameUpdated() {
    // 计算逻辑 deltaTime
    qint64 currentMs = QDateTime::currentMSecsSinceEpoch();
    float deltaTime  = 0.033f;  // 默认值
    if (m_lastLogicTimeMs != 0) {
        deltaTime = static_cast<float>(currentMs - m_lastLogicTimeMs) / 1000.0f;
    }
    m_lastLogicTimeMs = currentMs;

    // 更新游戏逻辑
    if (m_gameController) {
        m_gameController->tick(deltaTime);
    }

    // 更新效果动画
    for (auto& effect : m_attackEffects) {
        effect.progress += 0.1f;
    }
    m_attackEffects.erase(std::remove_if(m_attackEffects.begin(),
                                         m_attackEffects.end(),
                                         [](SimpleAttackEffect const& e) { return e.progress >= 1.0f; }),
                          m_attackEffects.end());

    // 更新闪烁效果
    for (auto& flash : m_flashPositions) {
        flash.remainingFrames--;
    }
    m_flashPositions.erase(std::remove_if(m_flashPositions.begin(),
                                          m_flashPositions.end(),
                                          [](FlashEffect const& f) { return f.remainingFrames <= 0; }),
                           m_flashPositions.end());

    update();  // 触发重绘
}

void GameViewWidget::setTowerPlacementMode(TowerType type) {
    m_placingTowerType = type;
    setCursor(Qt::CrossCursor);
}

void GameViewWidget::clearTowerPlacementMode() {
    m_placingTowerType = std::nullopt;
    setCursor(Qt::ArrowCursor);
}

void GameViewWidget::showTowerSelectionMenu(QPoint const& globalPos, QPoint const& gridPos) {
    // 检查是否可以在此位置放置塔
    if (!canPlaceTowerAt(gridPos, TowerType::MELEE) && !canPlaceTowerAt(gridPos, TowerType::RANGED)) {
        return;  // 无法在此位置放置任何塔
    }

    QMenu menu(this);
    menu.setTitle("选择塔类型");

    // 添加塔选项
    QAction* meleeAction  = nullptr;
    QAction* rangedAction = nullptr;

    if (canPlaceTowerAt(gridPos, TowerType::MELEE)) {
        meleeAction = menu.addAction("近战塔 (成本: 100)");
        meleeAction->setData(static_cast<int>(TowerType::MELEE));
    }

    if (canPlaceTowerAt(gridPos, TowerType::RANGED)) {
        rangedAction = menu.addAction("远程塔 (成本: 150)");
        rangedAction->setData(static_cast<int>(TowerType::RANGED));
    }

    // 显示菜单并获取用户选择
    QAction* selectedAction = menu.exec(globalPos);
    if (selectedAction) {
        TowerType towerType = static_cast<TowerType>(selectedAction->data().toInt());
        emit placeTowerRequested(towerType, gridPos);
    }
}

void GameViewWidget::onAttackPerformed(Unit* attacker, Unit* target, int /*damage*/) {
    if (attacker == nullptr || target == nullptr) {
        return;
    }

    // 创建攻击效果（简单的直线弹道）
    SimpleAttackEffect effect;
    effect.startPos = attacker->getPosition();
    effect.endPos   = target->getPosition();
    effect.progress = 0.0f;
    effect.color    = Qt::yellow;

    // 根据攻击者类型确定投射物贴图
    int projectileId = -1;
    if (auto* towerPtr = dynamic_cast<Tower*>(attacker)) {
        auto const& visual = GameData::getInstance().getVisualDataForTower(towerPtr->getTowerType());
        projectileId       = visual.projectileTileId;
    } else if (auto* enemyPtr = dynamic_cast<Enemy*>(attacker)) {
        projectileId = GameData::getInstance().getEnemyAttackEffectTileId(enemyPtr->getEnemyType());
    }
    effect.projectileTileId = projectileId;

    // 判断目标是否会死亡（在调用时目标已减血）
    effect.targetWillDie = (target != nullptr && target->getHealth() <= 0);
    if (effect.targetWillDie && target != nullptr) {
        effect.targetLastPos = target->getPosition();

        // 粗略推断敌人贴图（更准确可根据词缀获取）
        EnemyType eType = EnemyType::MELEE;
        if (target->getType() == UnitType::ENEMY_MELEE) {
            eType = EnemyType::MELEE;
        }
        effect.targetTileId = GameData::getInstance().getEnemyTileIdByAffixes(eType, false, false);
    }

    m_attackEffects.push_back(effect);

    // 在目标位置创建闪烁效果
    FlashEffect flash;
    flash.position        = target->getPosition();
    flash.remainingFrames = 20;  // 持续帧数
    m_flashPositions.push_back(flash);
}