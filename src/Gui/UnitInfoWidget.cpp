#include "TowerDefense/Gui/UnitInfoWidget.hpp"

// TODO: 需要包含实际的Unit, Tower, Enemy类定义
// #include "TowerDefense/GameLogic/Unit.hpp"
// #include "TowerDefense/GameLogic/Tower.hpp"
// #include "TowerDefense/GameLogic/Enemy.hpp"

UnitInfoWidget::UnitInfoWidget(QWidget* parent) : QWidget(parent) {
    setupUI();
}

void UnitInfoWidget::setupUI() {
    // 创建主布局
    auto* mainLayout = new QVBoxLayout(this);

    // 创建分组框
    m_groupBox = new QGroupBox("单位信息", this);

    // 创建标签
    m_unitTypeLabel = new QLabel("类型: 无选择", this);
    m_healthLabel   = new QLabel("生命: --", this);
    m_damageLabel   = new QLabel("伤害: --", this);
    m_rangeLabel    = new QLabel("范围: --", this);
    m_affixLabel    = new QLabel("词缀: --", this);

    // 设置标签样式
    m_unitTypeLabel->setStyleSheet("font-weight: bold;");

    // 创建分组框布局
    auto* groupLayout = new QVBoxLayout(m_groupBox);
    groupLayout->addWidget(m_unitTypeLabel);
    groupLayout->addWidget(m_healthLabel);
    groupLayout->addWidget(m_damageLabel);
    groupLayout->addWidget(m_rangeLabel);
    groupLayout->addWidget(m_affixLabel);

    // 添加到主布局
    mainLayout->addWidget(m_groupBox);
    mainLayout->addStretch();

    setLayout(mainLayout);

    // 初始状态显示无选择
    clearUnitInfo();
}

void UnitInfoWidget::showUnitInfo(Unit* unit) {
    m_currentUnit = unit;

    if (!unit) {
        clearUnitInfo();
        return;
    }

    // TODO: 目前使用占位符，实际需要根据Unit类的接口实现
    m_unitTypeLabel->setText("类型: 单位");
    m_healthLabel->setText("生命: 100/100");
    m_damageLabel->setText("伤害: 25");
    m_rangeLabel->setText("范围: 1");
    m_affixLabel->setText("词缀: 无");

    // 如果是塔类型
    // if (auto* tower = dynamic_cast<Tower*>(unit)) {
    //     showTowerInfo(tower);
    // }
    // // 如果是敌人类型
    // else if (auto* enemy = dynamic_cast<Enemy*>(unit)) {
    //     showEnemyInfo(enemy);
    // }
}

void UnitInfoWidget::clearUnitInfo() {
    m_currentUnit = nullptr;
    m_unitTypeLabel->setText("类型: 无选择");
    m_healthLabel->setText("生命: --");
    m_damageLabel->setText("伤害: --");
    m_rangeLabel->setText("范围: --");
    m_affixLabel->setText("词缀: --");
}

void UnitInfoWidget::showTowerInfo(Tower const* tower) {
    // TODO: 实现塔信息显示
    (void)tower;  // 避免未使用参数警告

    m_unitTypeLabel->setText("类型: 塔");
    // m_healthLabel->setText(QString("生命: %1/%2").arg(tower->getCurrentHealth()).arg(tower->getMaxHealth()));
    // m_damageLabel->setText(QString("伤害: %1").arg(tower->getDamage()));
    // m_rangeLabel->setText(QString("范围: %1").arg(tower->getRange()));
    //
    // QString affixText = "词缀: ";
    // auto affixes = tower->getAffixes();
    // if (affixes.empty()) {
    //     affixText += "无";
    // } else {
    //     for (size_t i = 0; i < affixes.size(); ++i) {
    //         if (i > 0) affixText += ", ";
    //         affixText += getAffixName(affixes[i]);
    //     }
    // }
    // m_affixLabel->setText(affixText);
}

void UnitInfoWidget::showEnemyInfo(Enemy const* enemy) {
    // TODO: 实现敌人信息显示
    (void)enemy;  // 避免未使用参数警告

    m_unitTypeLabel->setText("类型: 敌人");
    // m_healthLabel->setText(QString("生命: %1/%2").arg(enemy->getCurrentHealth()).arg(enemy->getMaxHealth()));
    // m_damageLabel->setText(QString("伤害: %1").arg(enemy->getDamage()));
    // m_rangeLabel->setText(QString("速度: %1").arg(enemy->getSpeed()));
    //
    // QString affixText = "词缀: ";
    // auto affixes = enemy->getAffixes();
    // if (affixes.empty()) {
    //     affixText += "无";
    // } else {
    //     for (size_t i = 0; i < affixes.size(); ++i) {
    //         if (i > 0) affixText += ", ";
    //         affixText += getEnemyAffixName(affixes[i]);
    //     }
    // }
    // m_affixLabel->setText(affixText);
}