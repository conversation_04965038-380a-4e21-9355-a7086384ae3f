# 查找所需的 Qt6 组件
find_package(Qt6 REQUIRED COMPONENTS Core Gui Widgets) # 需要 Core, Gui ( for drawing types), Widgets (for UI elements)

# 定义GUI库
add_library(Gui STATIC
    MainWindow.cpp
    GameInfoWidget.cpp
    TowerSelectionWidget.cpp
    UnitInfoWidget.cpp
    AffixManagementWidget.cpp
    GameViewWidget.cpp
    UIAdapter.cpp
    ${CMAKE_SOURCE_DIR}/include/TowerDefense/Gui/MainWindow.hpp
    ${CMAKE_SOURCE_DIR}/include/TowerDefense/Gui/GameInfoWidget.hpp
    ${CMAKE_SOURCE_DIR}/include/TowerDefense/Gui/TowerSelectionWidget.hpp
    ${CMAKE_SOURCE_DIR}/include/TowerDefense/Gui/UnitInfoWidget.hpp
    ${CMAKE_SOURCE_DIR}/include/TowerDefense/Gui/AffixManagementWidget.hpp
    ${CMAKE_SOURCE_DIR}/include/TowerDefense/Gui/GameViewWidget.hpp
    ${CMAKE_SOURCE_DIR}/include/TowerDefense/Gui/UIAdapter.hpp
)

# 启用Qt的元对象编译器
set_target_properties(Gui PROPERTIES
    AUTOMOC ON
)

# 设置包含目录
target_include_directories(Gui PUBLIC

    # 允许外部目标包含 Gui 库的公共头文件
    $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

target_include_directories(Gui PRIVATE

    # 添加当前源目录
    ${CMAKE_CURRENT_SOURCE_DIR}

    # 添加构建目录，以便找到生成的UI头文件
    ${CMAKE_CURRENT_BINARY_DIR}

    # 添加自动生成的目录
    ${CMAKE_CURRENT_BINARY_DIR}/Gui_autogen/include
)

# 链接所需的库 (Keep as is)
target_link_libraries(Gui PRIVATE
    Core
    GameLogic
    Utils
    Qt6::Core
    Qt6::Gui
    Qt6::Widgets
)

# The commented out UI file handling is replaced by listing .ui in qt_add_library
# file(GLOB UI_FORMS "${CMAKE_CURRENT_SOURCE_DIR}/forms/*.ui")
# qt_wrap_ui(Gui_UI_HPP ${UI_FORMS})
# target_sources(Gui PRIVATE ${Gui_UI_HPP})