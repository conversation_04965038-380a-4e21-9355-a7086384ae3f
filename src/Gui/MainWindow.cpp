#include "TowerDefense/Gui/MainWindow.hpp"

#include "TowerDefense/Core/GameController.hpp"
#include "TowerDefense/GameLogic/Map.hpp"
#include "TowerDefense/Gui/AffixManagementWidget.hpp"
#include "TowerDefense/Gui/GameInfoWidget.hpp"
#include "TowerDefense/Gui/GameViewWidget.hpp"
#include "TowerDefense/Gui/TowerSelectionWidget.hpp"
#include "TowerDefense/Gui/UIAdapter.hpp"
#include "TowerDefense/Gui/UnitInfoWidget.hpp"
#include "TowerDefense/Utils/TileRenderer.hpp"

#include <QComboBox>
#include <QCoreApplication>
#include <QDir>
#include <QFileDialog>
#include <QFrame>
#include <QGroupBox>
#include <QHBoxLayout>
#include <QMenu>
#include <QMenuBar>
#include <QMessageBox>
#include <QPushButton>
#include <QScrollArea>
#include <QSplitter>
#include <QVBoxLayout>

MainWindow::MainWindow(QWidget* parent) : QMainWindow(parent) {
    setupUi();
    createWidgets();
    createMenus();
    initializeUIAdapter();
}

MainWindow::~MainWindow() = default;

// Implement the declared slots
void MainWindow::handleGameStateChanged(GameState newState) {
    // Prevent unused parameter warning
    (void)newState;
    // TODO: Update UI based on game state
}

void MainWindow::handleLivesChanged(int newLives, int amountChanged) {
    // Prevent unused parameter warnings
    (void)newLives;
    (void)amountChanged;
    // TODO: Update UI to show new lives count
}

void MainWindow::handleMoneyChanged(uint32_t newMoney, int amountChanged) {
    // Prevent unused parameter warnings
    (void)newMoney;
    (void)amountChanged;
    // TODO: Update UI to show new money count
}

void MainWindow::setupUi() {
    // 设置窗口属性
    setWindowTitle("塔防游戏");
    setMinimumSize(1024, 768);

    // 创建中央部件
    auto* centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    // 创建主布局（水平分割器）
    auto* mainSplitter = new QSplitter(Qt::Horizontal, this);

    // 创建游戏视图区域
    m_gameView = new GameViewWidget(this);
    m_gameView->setMinimumSize(640, 480);

    // 使用滚动区域包装游戏视图，使其在地图较大时可滚动而不是撑破布局
    m_gameScrollArea = new QScrollArea(this);
    m_gameScrollArea->setWidgetResizable(false);  // 固定内容大小，启用滚动条
    m_gameScrollArea->setFrameShape(QFrame::NoFrame);
    m_gameScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_gameScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_gameScrollArea->setWidget(m_gameView);

    // 创建右侧控制面板
    auto* rightPanel = new QWidget(this);
    rightPanel->setMaximumWidth(300);
    rightPanel->setMinimumWidth(250);

    // 添加到分割器
    mainSplitter->addWidget(m_gameScrollArea);
    mainSplitter->addWidget(rightPanel);
    mainSplitter->setStretchFactor(0, 3);  // 游戏视图占更多空间
    mainSplitter->setStretchFactor(1, 1);  // 控制面板固定宽度

    // 创建中央布局
    auto* centralLayout = new QHBoxLayout(centralWidget);
    centralLayout->addWidget(mainSplitter);
    centralLayout->setContentsMargins(5, 5, 5, 5);
}

void MainWindow::createWidgets() {
    // 获取右侧面板（通过中央部件的分割器获取）
    auto* centralWidget = this->centralWidget();
    auto* mainSplitter  = centralWidget->findChild<QSplitter*>();
    if (!mainSplitter) {
        return;
    }

    auto* rightPanel = qobject_cast<QWidget*>(mainSplitter->widget(1));
    if (!rightPanel) {
        return;
    }

    auto* rightLayout = new QVBoxLayout(rightPanel);

    // 创建GUI组件
    m_gameInfo        = new GameInfoWidget(this);
    m_towerSelection  = new TowerSelectionWidget(this);
    m_unitInfo        = new UnitInfoWidget(this);
    m_affixManagement = new AffixManagementWidget(this);

    // 添加组件到右侧面板
    rightLayout->addWidget(m_gameInfo);
    rightLayout->addWidget(m_towerSelection);
    rightLayout->addWidget(m_unitInfo);
    rightLayout->addWidget(m_affixManagement);

    // === 地图管理面板 ===
    auto* mapGroup  = new QGroupBox(tr("地图管理"), this);
    auto* mapLayout = new QVBoxLayout(mapGroup);

    m_builtinMapCombo = new QComboBox(mapGroup);

    // 扫描项目中的 assets/data/maps 目录（优先当前工作目录，便于开发时直接使用源文件）
    QDir mapsDir(QDir::currentPath() + "/assets/data/maps");
    if (!mapsDir.exists()) {
        // 回退到可执行文件所在目录下的 assets 目录（安装包或构建目录）
        mapsDir = QDir(QCoreApplication::applicationDirPath() + "/assets/data/maps");
    }
    QStringList filters;
    filters << "*.map";
    QStringList mapFiles = mapsDir.entryList(filters, QDir::Files);
    for (QString const& file : mapFiles) {
        m_builtinMapCombo->addItem(file, mapsDir.filePath(file));
    }

    // 初始不选择任何地图，等待用户操作
    m_builtinMapCombo->setCurrentIndex(-1);

    m_importMapButton = new QPushButton(tr("导入地图..."), mapGroup);
    m_exportMapButton = new QPushButton(tr("导出地图..."), mapGroup);

    mapLayout->addWidget(m_builtinMapCombo);
    mapLayout->addWidget(m_importMapButton);
    mapLayout->addWidget(m_exportMapButton);

    rightLayout->addWidget(mapGroup);

    // 信号连接
    connect(m_importMapButton, &QPushButton::clicked, this, &MainWindow::onImportMap);
    connect(m_exportMapButton, &QPushButton::clicked, this, &MainWindow::onExportMap);
    connect(m_builtinMapCombo,
            QOverload<int>::of(&QComboBox::currentIndexChanged),
            this,
            &MainWindow::onBuiltinMapComboChanged);

    rightLayout->addStretch();  // 添加弹性空间
}

// 获取GUI组件的访问器方法
GameViewWidget* MainWindow::getGameViewWidget() const {
    return m_gameView;
}

GameInfoWidget* MainWindow::getGameInfoWidget() const {
    return m_gameInfo;
}

TowerSelectionWidget* MainWindow::getTowerSelectionWidget() const {
    return m_towerSelection;
}

UnitInfoWidget* MainWindow::getUnitInfoWidget() const {
    return m_unitInfo;
}

AffixManagementWidget* MainWindow::getAffixManagementWidget() const {
    return m_affixManagement;
}

void MainWindow::initializeUIAdapter() {
    // 确保GameViewWidget有GameController引用
    if (m_gameView) {
        m_gameView->setGameController(&GameController::getInstance());
        qDebug() << "MainWindow: GameController set in GameViewWidget";
    }

    // 创建UI适配器
    m_uiAdapter = std::make_unique<UIAdapter>(&GameController::getInstance(), this, this);
    m_uiAdapter->setupConnections();
}

void MainWindow::setTileRenderer(std::shared_ptr<TileRenderer> tileRenderer) {
    if (m_gameView) {
        m_gameView->setTileRenderer(tileRenderer);
        qDebug() << "MainWindow: TileRenderer set in GameViewWidget";
    }
}

void MainWindow::setMap(std::shared_ptr<Map> map) {
    if (m_gameView) {
        m_gameView->setMap(map);
        qDebug() << "MainWindow: Map set in GameViewWidget";
    }
}

void MainWindow::createMenus() {
    // 创建菜单栏
    m_menuBar = new QMenuBar(this);
    setMenuBar(m_menuBar);

    // 地图菜单
    m_mapMenu = new QMenu(tr("地图(&M)"), this);
    m_menuBar->addMenu(m_mapMenu);

    QAction* importAction = m_mapMenu->addAction(tr("导入地图..."));
    QAction* exportAction = m_mapMenu->addAction(tr("导出当前地图..."));

    connect(importAction, &QAction::triggered, this, &MainWindow::onImportMap);
    connect(exportAction, &QAction::triggered, this, &MainWindow::onExportMap);

    // 内置地图子菜单
    m_builtinMapsSubMenu = m_mapMenu->addMenu(tr("选择内置地图"));

    // 扫描项目中的 assets/data/maps 目录（优先当前工作目录，便于开发时直接使用源文件）
    QDir mapsDir(QDir::currentPath() + "/assets/data/maps");
    if (!mapsDir.exists()) {
        // 回退到可执行文件所在目录下的 assets 目录（安装包或构建目录）
        mapsDir = QDir(QCoreApplication::applicationDirPath() + "/assets/data/maps");
    }
    QStringList filters;
    filters << "*.map";
    QStringList mapFiles = mapsDir.entryList(filters, QDir::Files);
    for (QString const& file : mapFiles) {
        QAction* mapAct = m_builtinMapsSubMenu->addAction(file);
        mapAct->setData(mapsDir.filePath(file));
        connect(mapAct, &QAction::triggered, this, &MainWindow::onBuiltinMapSelected);
    }
}

void MainWindow::onImportMap() {
    QString filePath
        = QFileDialog::getOpenFileName(this, tr("选择地图文件"), QDir::homePath(), tr("Tower Defense Map (*.map)"));
    if (filePath.isEmpty()) {
        return;
    }

    // 提醒用户语法要求
    QMessageBox::information(this, tr("导入自定义地图"), tr("请确保您的自定义地图遵守 StandardMap.map 的语法规则。"));

    loadMapFromPath(filePath);
}

void MainWindow::onExportMap() {
    if (!GameController::getInstance().getCurrentMap()) {
        QMessageBox::warning(this, tr("导出地图"), tr("当前没有加载任何地图。"));
        return;
    }

    QString filePath = QFileDialog::getSaveFileName(
        this, tr("导出地图为"), QDir::homePath() + "/exported.map", tr("Tower Defense Map (*.map)"));
    if (filePath.isEmpty()) {
        return;
    }

    if (!GameController::getInstance().getCurrentMap()->saveToFile(filePath.toStdString())) {
        QMessageBox::critical(this, tr("导出地图"), tr("导出失败，请检查文件路径或权限。"));
    } else {
        QMessageBox::information(this, tr("导出地图"), tr("地图已成功导出！"));
    }
}

void MainWindow::onBuiltinMapSelected() {
    QAction* act = qobject_cast<QAction*>(sender());
    if (!act) {
        return;
    }
    QString filePath = act->data().toString();
    loadMapFromPath(filePath);
}

void MainWindow::loadMapFromPath(QString const& filePath) {
    if (filePath.isEmpty()) {
        return;
    }

    GameController& controller = GameController::getInstance();
    controller.startGame(filePath.toStdString());

    // 将新地图设置到视图
    std::shared_ptr<Map> currentMap = controller.getCurrentMap();
    if (currentMap) {
        setMap(currentMap);
    } else {
        // 清除当前显示的地图，避免继续使用上一张
        setMap(nullptr);
        QMessageBox::critical(
            this, tr("加载地图"), tr("地图加载失败：Tiles与Paths不一致或文件格式错误。请修正后再试。"));
    }
}

void MainWindow::onBuiltinMapComboChanged(int index) {
    if (index < 0) {
        return;
    }
    QString filePath = m_builtinMapCombo->itemData(index).toString();
    loadMapFromPath(filePath);
}
