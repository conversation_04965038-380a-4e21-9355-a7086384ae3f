#include "TowerDefense/Gui/GameInfoWidget.hpp"

#include <QHBoxLayout>
#include <QPushButton>

GameInfoWidget::GameInfoWidget(QWidget* parent) : QWidget(parent) {
    setupUI();
}

void GameInfoWidget::setupUI() {
    // 创建主布局
    auto* mainLayout = new QVBoxLayout(this);

    // 创建分组框
    m_groupBox = new QGroupBox("游戏信息", this);

    // 创建标签
    m_moneyLabel = new QLabel(QString("金币: %1").arg(m_currentMoney), this);
    m_livesLabel = new QLabel(QString("生命: %1").arg(m_currentLives), this);
    m_waveLabel  = new QLabel(QString("波次: %1/%2").arg(m_currentWave).arg(m_totalWaves), this);

    m_countdownLabel = new QLabel("", this);
    m_countdownLabel->setStyleSheet("font-weight: bold; color: orange;");
    m_countdownLabel->hide();

    // 创建开始波次按钮
    m_startWaveButton = new QPushButton("开始下一波", this);
    connect(m_startWaveButton, &QPushButton::clicked, this, &GameInfoWidget::startWaveClicked);

    // 设置标签样式
    m_moneyLabel->setStyleSheet("font-weight: bold; color: gold;");
    m_livesLabel->setStyleSheet("font-weight: bold; color: red;");
    m_waveLabel->setStyleSheet("font-weight: bold; color: blue;");

    // 创建分组框布局
    auto* groupLayout = new QVBoxLayout(m_groupBox);
    groupLayout->addWidget(m_moneyLabel);
    groupLayout->addWidget(m_livesLabel);
    groupLayout->addWidget(m_waveLabel);
    groupLayout->addWidget(m_countdownLabel);
    groupLayout->addWidget(m_startWaveButton);

    // 添加到主布局
    mainLayout->addWidget(m_groupBox);
    mainLayout->addStretch();  // 添加弹性空间

    setLayout(mainLayout);
}

void GameInfoWidget::updateMoney(int amount) {
    m_currentMoney = amount;
    m_moneyLabel->setText(QString("金币: %1").arg(m_currentMoney));
}

void GameInfoWidget::updateLives(int lives) {
    m_currentLives = lives;
    m_livesLabel->setText(QString("生命: %1").arg(m_currentLives));
}

void GameInfoWidget::updateWaveProgress(int currentWave, int totalWaves) {
    m_currentWave = currentWave;
    m_totalWaves  = totalWaves;
    m_waveLabel->setText(QString("波次: %1/%2").arg(m_currentWave).arg(m_totalWaves));
}

void GameInfoWidget::updateCountdown(int secondsRemaining) {
    m_countdownSec = secondsRemaining;
    if (secondsRemaining >= 0) {
        m_countdownLabel->setText(QString("下波倒计时: %1s").arg(secondsRemaining));
        m_countdownLabel->show();
    } else {
        m_countdownLabel->hide();
    }
}

void GameInfoWidget::setStartWaveButtonEnabled(bool enabled) {
    m_startWaveButton->setEnabled(enabled);
    m_startWaveButton->setVisible(enabled);
}

// MOC include will be automatically generated by CMake