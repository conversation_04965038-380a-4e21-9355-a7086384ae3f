add_library(Utils STATIC
    Utils.cpp
    TilesheetConfig.cpp
    TileRenderer.cpp
    ../../include/TowerDefense/Utils/TilesheetConfig.hpp
    ../../include/TowerDefense/Utils/TileRenderer.hpp
)

target_include_directories(Utils PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../../include>
    $<INSTALL_INTERFACE:include>
)

# Link with Qt components needed for TilesheetConfig
find_package(Qt6 REQUIRED COMPONENTS Core Gui)
target_link_libraries(Utils PRIVATE Qt6::Core Qt6::Gui)
