#include "TowerDefense/Utils/TilesheetConfig.hpp"

#include <QDebug>
#include <QDir>
#include <QFile>
#include <QJsonArray>
#include <QJsonParseError>

TilesheetConfig::TilesheetConfig(QString const& tilesheetPath, QString const& configPath)
    : m_tilesheetPath(tilesheetPath), m_configPath(configPath) {
    qDebug() << "TilesheetConfig: Initializing with tilesheet:" << tilesheetPath;
    qDebug() << "TilesheetConfig: Config file:" << configPath;

    // Load the tilesheet image
    if (!loadTilesheet(tilesheetPath)) {
        qWarning() << "TilesheetConfig: Failed to load tilesheet image";
        return;
    }

    // Load the configuration
    if (!loadConfiguration(configPath)) {
        qWarning() << "TilesheetConfig: Failed to load configuration";
        return;
    }

    m_loaded = true;
    qDebug() << "TilesheetConfig: Successfully initialized with" << m_tileIdMap.size() << "tile mappings";
}

bool TilesheetConfig::isLoaded() const {
    return m_loaded;
}

bool TilesheetConfig::loadTilesheet(QString const& path) {
    if (!m_tilesheet.load(path)) {
        qWarning() << "TilesheetConfig: Failed to load tilesheet from:" << path;

        // Try to find the file in different locations
        QStringList searchPaths = {
            path,                                         // 原始路径
            QDir::currentPath() + "/" + path,             // 当前目录 + 相对路径
            "../" + path,                                 // 上一级目录 + 相对路径
            "../../" + path,                              // 上两级目录 + 相对路径
            "../../../" + path,                           // 上三级目录 + 相对路径
            "../../../../" + path,                        // 上四级目录 + 相对路径
            QDir::currentPath() + "/build/" + path,       // 构建目录
            QDir::currentPath() + "/../" + path,          // 从构建目录到源目录
            QDir::currentPath() + "/../../" + path,       // 从深层构建目录到源目录
            QDir::currentPath() + "/../../../" + path,    // 从更深层构建目录到源目录
            QDir::currentPath() + "/../../../../" + path  // 从最深层构建目录到源目录
        };

        for (QString const& searchPath : searchPaths) {
            qDebug() << "TilesheetConfig: Trying path:" << searchPath;
            if (m_tilesheet.load(searchPath)) {
                qDebug() << "TilesheetConfig: Successfully loaded tilesheet from:" << searchPath;
                return true;
            }
        }

        return false;
    }

    qDebug() << "TilesheetConfig: Loaded tilesheet" << m_tilesheet.size() << "from:" << path;
    return true;
}

bool TilesheetConfig::loadConfiguration(QString const& path) {
    QFile file(path);

    // Try different paths if the file doesn't exist
    QStringList searchPaths = {
        path,                                         // 原始路径
        QDir::currentPath() + "/" + path,             // 当前目录 + 相对路径
        "../" + path,                                 // 上一级目录 + 相对路径
        "../../" + path,                              // 上两级目录 + 相对路径
        "../../../" + path,                           // 上三级目录 + 相对路径
        "../../../../" + path,                        // 上四级目录 + 相对路径
        QDir::currentPath() + "/build/" + path,       // 构建目录
        QDir::currentPath() + "/../" + path,          // 从构建目录到源目录
        QDir::currentPath() + "/../../" + path,       // 从深层构建目录到源目录
        QDir::currentPath() + "/../../../" + path,    // 从更深层构建目录到源目录
        QDir::currentPath() + "/../../../../" + path  // 从最深层构建目录到源目录
    };

    bool fileOpened = false;
    QString actualPath;

    for (QString const& searchPath : searchPaths) {
        file.setFileName(searchPath);
        if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            actualPath = searchPath;
            fileOpened = true;
            qDebug() << "TilesheetConfig: Found config file at:" << searchPath;
            break;
        }
    }

    if (!fileOpened) {
        qWarning() << "TilesheetConfig: Could not open config file. Tried paths:";
        for (QString const& searchPath : searchPaths) {
            qWarning() << "  -" << searchPath;
        }
        return false;
    }

    // Read and parse JSON
    QByteArray data = file.readAll();
    file.close();

    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        qWarning() << "TilesheetConfig: JSON parse error:" << parseError.errorString();
        return false;
    }

    QJsonObject root = doc.object();

    // Load metadata
    if (root.contains("metadata")) {
        QJsonObject metadata = root["metadata"].toObject();
        m_tileSize           = metadata["tile_size"].toInt(64);
        m_gridWidth          = metadata["grid_width"].toInt(23);
        m_gridHeight         = metadata["grid_height"].toInt(13);
        m_maxTileId          = metadata["total_tiles"].toInt(299) - 1;  // Convert count to max ID

        qDebug() << "TilesheetConfig: Loaded metadata - tile size:" << m_tileSize << "grid:" << m_gridWidth << "x"
                 << m_gridHeight << "max tile ID:" << m_maxTileId;
    }

    // Parse tiles section
    if (root.contains("tiles")) {
        parseTilesSection(root["tiles"].toObject());
    }

    // Parse UI elements section
    if (root.contains("ui_elements")) {
        parseUIElementsSection(root["ui_elements"].toObject());
    }

    qDebug() << "TilesheetConfig: Configuration loaded successfully";
    return true;
}

void TilesheetConfig::parseTilesSection(QJsonObject const& tilesObject) {
    // Iterate through all tile entries
    for (auto it = tilesObject.begin(); it != tilesObject.end(); ++it) {
        QJsonObject tileData = it.value().toObject();

        if (tileData.contains("id") && tileData.contains("tile_id")) {
            QString elementId = tileData["id"].toString();
            int tileId        = tileData["tile_id"].toInt();

            m_tileIdMap[elementId] = tileId;
            qDebug() << "TilesheetConfig: Mapped" << elementId << "to tile ID" << tileId;
        }

        // Handle obstacle tiles with base and overlay
        if (tileData.contains("base_tile_id")) {
            QString elementId = tileData["id"].toString();
            int baseTileId    = tileData["base_tile_id"].toInt();

            // Store base tile ID for obstacles
            m_tileIdMap[elementId + "_base"] = baseTileId;

            if (tileData.contains("overlay_tile_id")) {
                int overlayTileId                   = tileData["overlay_tile_id"].toInt();
                m_tileIdMap[elementId + "_overlay"] = overlayTileId;
            }
        }
    }
}

void TilesheetConfig::parseUIElementsSection(QJsonObject const& uiObject) {
    // Parse direct UI elements
    for (auto it = uiObject.begin(); it != uiObject.end(); ++it) {
        QJsonValue value = it.value();

        if (value.isObject()) {
            QJsonObject uiData = value.toObject();
            if (uiData.contains("id") && uiData.contains("tile_id")) {
                QString elementId = uiData["id"].toString();
                int tileId        = uiData["tile_id"].toInt();

                m_tileIdMap[elementId] = tileId;
                qDebug() << "TilesheetConfig: Mapped UI element" << elementId << "to tile ID" << tileId;
            }
        } else if (value.isObject()) {
            // Handle nested UI sections like ui_special
            QJsonObject nestedSection = value.toObject();
            parseUIElementsSection(nestedSection);
        }
    }

    // Handle ui_special section separately if it exists
    if (uiObject.contains("ui_special")) {
        QJsonObject specialSection = uiObject["ui_special"].toObject();
        parseUIElementsSection(specialSection);
    }
}

QPoint TilesheetConfig::calculateGridPosition(int tileId) const {
    if (!isValidTileId(tileId)) {
        qWarning() << "TilesheetConfig: Invalid tile ID:" << tileId;
        return QPoint(0, 0);
    }

    int gridX = tileId % m_gridWidth;
    int gridY = tileId / m_gridWidth;
    return QPoint(gridX, gridY);
}

QRect TilesheetConfig::calculatePixelRect(int tileId) const {
    QPoint gridPos = calculateGridPosition(tileId);
    return QRect(gridPos.x() * m_tileSize, gridPos.y() * m_tileSize, m_tileSize, m_tileSize);
}

bool TilesheetConfig::isValidTileId(int tileId) const {
    return tileId >= 0 && tileId <= m_maxTileId;
}

QRect TilesheetConfig::getTileRect(QString const& elementId) const {
    auto it = m_tileIdMap.find(elementId);
    if (it == m_tileIdMap.end()) {
        qWarning() << "TilesheetConfig: Element not found:" << elementId;
        return QRect(0, 0, m_tileSize, m_tileSize);
    }

    return calculatePixelRect(it->second);
}

QRect TilesheetConfig::getTileRectById(int tileId) const {
    return calculatePixelRect(tileId);
}

QPixmap TilesheetConfig::getTilePixmap(QString const& elementId) const {
    if (!m_loaded) {
        qWarning() << "TilesheetConfig: Not loaded, cannot get pixmap for" << elementId;
        return QPixmap();
    }

    QRect rect = getTileRect(elementId);
    return m_tilesheet.copy(rect);
}

QPixmap TilesheetConfig::getTilePixmapById(int tileId) const {
    if (!m_loaded) {
        qWarning() << "TilesheetConfig: Not loaded, cannot get pixmap for tile ID" << tileId;
        return QPixmap();
    }

    QRect rect = getTileRectById(tileId);
    return m_tilesheet.copy(rect);
}

int TilesheetConfig::getTileId(QString const& elementId) const {
    auto it = m_tileIdMap.find(elementId);
    if (it == m_tileIdMap.end()) {
        return -1;
    }
    return it->second;
}
