#include "TowerDefense/Utils/TileRenderer.hpp"

#include <QDebug>

TileRenderer::Tile<PERSON>enderer(std::shared_ptr<TilesheetConfig> tilesheetConfig) : m_tilesheetConfig(tilesheetConfig) {
    if (!m_tilesheetConfig) {
        qWarning() << "TileRenderer: Null tilesheet config provided";
    } else if (!m_tilesheetConfig->isLoaded()) {
        qWarning() << "TileRenderer: Tilesheet config is not loaded";
    } else {
        qDebug() << "TileRenderer: Initialized successfully";
    }
}

bool TileRenderer::isReady() const {
    return m_tilesheetConfig && m_tilesheetConfig->isLoaded();
}

void TileRenderer::renderMapTile(QPainter& painter, QRect const& targetRect, TileType tileType) {
    if (!isReady()) {
        qWarning() << "TileRenderer: Not ready, cannot render map tile";
        return;
    }

    QString elementId = getElementIdForTileType(tileType);
    if (elementId.isEmpty()) {
        qWarning() << "TileRenderer: No element ID found for tile type" << static_cast<int>(tileType);
        return;
    }

    // Debug output for first few tiles (can be removed in production)
    static int renderCount = 0;
    if (renderCount < 3) {
        qDebug() << "TileRenderer: Successfully rendering" << elementId << "at" << targetRect;
        renderCount++;
    }

    renderTileByElementId(painter, targetRect, elementId);
}

void TileRenderer::renderTileByElementId(QPainter& painter, QRect const& targetRect, QString const& elementId) {
    if (!isReady()) {
        qWarning() << "TileRenderer: Not ready, cannot render tile" << elementId;
        return;
    }

    QPixmap tilePixmap = m_tilesheetConfig->getTilePixmap(elementId);
    if (tilePixmap.isNull()) {
        qWarning() << "TileRenderer: Failed to get pixmap for element" << elementId;
        return;
    }

    painter.drawPixmap(targetRect, tilePixmap);
}

void TileRenderer::renderTileById(QPainter& painter, QRect const& targetRect, int tileId) {
    if (!isReady()) {
        qWarning() << "TileRenderer: Not ready, cannot render tile ID" << tileId;
        return;
    }

    QPixmap tilePixmap = m_tilesheetConfig->getTilePixmapById(tileId);
    if (tilePixmap.isNull()) {
        qWarning() << "TileRenderer: Failed to get pixmap for tile ID" << tileId;
        return;
    }

    painter.drawPixmap(targetRect, tilePixmap);
}

void TileRenderer::renderObstacleTile(QPainter& painter, QRect const& targetRect) {
    if (!isReady()) {
        qWarning() << "TileRenderer: Not ready, cannot render obstacle tile";
        return;
    }

    // Render base ground first
    QPixmap basePixmap = m_tilesheetConfig->getTilePixmap("obstacle_base");
    if (!basePixmap.isNull()) {
        painter.drawPixmap(targetRect, basePixmap);
    } else {
        // Fallback to direct tile ID if element mapping doesn't exist
        QPixmap fallbackBase = m_tilesheetConfig->getTilePixmapById(24);
        if (!fallbackBase.isNull()) {
            painter.drawPixmap(targetRect, fallbackBase);
        }
    }

    // Render overlay object
    QPixmap overlayPixmap = m_tilesheetConfig->getTilePixmap("obstacle_overlay");
    if (!overlayPixmap.isNull()) {
        painter.drawPixmap(targetRect, overlayPixmap);
    } else {
        // Fallback to direct tile ID
        QPixmap fallbackOverlay = m_tilesheetConfig->getTilePixmapById(130);
        if (!fallbackOverlay.isNull()) {
            painter.drawPixmap(targetRect, fallbackOverlay);
        }
    }
}

void TileRenderer::renderNumber(QPainter& painter, QPoint const& position, int number, int digitSpacing) {
    if (!isReady()) {
        qWarning() << "TileRenderer: Not ready, cannot render number";
        return;
    }

    if (digitSpacing < 0) {
        digitSpacing = getTileSize();
    }

    QString numberStr = QString::number(number);
    QPoint currentPos = position;

    for (int i = 0; i < numberStr.length(); ++i) {
        int digit = numberStr[i].digitValue();
        if (digit >= 0 && digit <= 9) {
            renderDigit(painter, currentPos, digit);
        }
        currentPos.setX(currentPos.x() + digitSpacing);
    }
}

void TileRenderer::renderScore(QPainter& painter, QPoint const& position, int score) {
    if (!isReady()) {
        qWarning() << "TileRenderer: Not ready, cannot render score";
        return;
    }

    // Render dollar sign first
    QPixmap dollarPixmap = m_tilesheetConfig->getTilePixmap("dollar");
    if (!dollarPixmap.isNull()) {
        painter.drawPixmap(position, dollarPixmap);
    }

    // Render the score number after the dollar sign
    QPoint numberPos = position + QPoint(getTileSize(), 0);
    renderNumber(painter, numberPos, score);
}

int TileRenderer::getTileSize() const {
    if (!isReady()) {
        return 64;  // Default fallback
    }
    return m_tilesheetConfig->getTileSize();
}

QPixmap TileRenderer::getTilePixmap(QString const& elementId) const {
    if (!isReady()) {
        return QPixmap();
    }
    return m_tilesheetConfig->getTilePixmap(elementId);
}

QPixmap TileRenderer::getTilePixmapById(int tileId) const {
    if (!isReady()) {
        return QPixmap();
    }
    return m_tilesheetConfig->getTilePixmapById(tileId);
}

QString TileRenderer::getElementIdForTileType(TileType tileType) const {
    switch (tileType) {
        case TileType::BUILDABLE_GROUND:
            return "buildable_ground";
        case TileType::PATH:
            return "path";
        case TileType::START_POINT:
            return "start_point";
        case TileType::END_POINT:
            return "end_point";
        case TileType::OBSTACLE:
            return "obstacle";
        case TileType::INVALID:
        default:
            return QString();
    }
}

void TileRenderer::renderDigit(QPainter& painter, QPoint const& position, int digit) {
    if (digit < 0 || digit > 9) {
        qWarning() << "TileRenderer: Invalid digit" << digit;
        return;
    }

    QString digitId     = QString("digit_%1").arg(digit);
    QPixmap digitPixmap = m_tilesheetConfig->getTilePixmap(digitId);

    if (!digitPixmap.isNull()) {
        painter.drawPixmap(position, digitPixmap);
    } else {
        qWarning() << "TileRenderer: Failed to get pixmap for digit" << digit;
    }
}
