#include "include/TowerDefense/GameLogic/Map.hpp"
#include <QCoreApplication>
#include <QDebug>
#include <iostream>

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    // Test loading the ComplexSnakeMap.map file
    Map testMap;
    std::string mapPath = "assets/data/maps/ComplexSnakeMap.map";
    
    std::cout << "Testing map loading for: " << mapPath << std::endl;
    
    bool success = testMap.loadFromFile(mapPath);
    
    if (success) {
        std::cout << "✓ Map loaded successfully!" << std::endl;
        std::cout << "Map dimensions: " << testMap.getWidth() << "x" << testMap.getHeight() << std::endl;
        std::cout << "Tile size: " << testMap.getTileSize() << std::endl;
        std::cout << "Number of paths: " << testMap.getPathCount() << std::endl;
        
        // Test some specific coordinates
        std::cout << "\nTesting specific tile types:" << std::endl;
        std::cout << "Tile at (2,2): " << static_cast<int>(testMap.getTileType(2, 2)) << " (should be START_POINT = 2)" << std::endl;
        std::cout << "Tile at (17,12): " << static_cast<int>(testMap.getTileType(17, 12)) << " (should be END_POINT = 3)" << std::endl;
        std::cout << "Tile at (3,2): " << static_cast<int>(testMap.getTileType(3, 2)) << " (should be PATH = 0)" << std::endl;
        std::cout << "Tile at (1,1): " << static_cast<int>(testMap.getTileType(1, 1)) << " (should be BUILDABLE_GROUND = 1)" << std::endl;
        
        // Test path waypoints
        if (testMap.getPathCount() > 0) {
            auto const& path = testMap.getPath(0);
            std::cout << "\nPath 0 has " << path.size() << " waypoints" << std::endl;
            if (!path.empty()) {
                std::cout << "First waypoint: (" << path[0].x() << ", " << path[0].y() << ")" << std::endl;
                std::cout << "Last waypoint: (" << path.back().x() << ", " << path.back().y() << ")" << std::endl;
            }
        }
        
    } else {
        std::cout << "✗ Failed to load map!" << std::endl;
        return 1;
    }
    
    // Test loading the StandardMap.map file for comparison
    std::cout << "\n" << std::string(50, '=') << std::endl;
    std::cout << "Testing StandardMap.map for comparison:" << std::endl;
    
    Map standardMap;
    std::string standardMapPath = "assets/data/maps/StandardMap.map";
    
    bool standardSuccess = standardMap.loadFromFile(standardMapPath);
    
    if (standardSuccess) {
        std::cout << "✓ StandardMap loaded successfully!" << std::endl;
        std::cout << "Map dimensions: " << standardMap.getWidth() << "x" << standardMap.getHeight() << std::endl;
        std::cout << "Tile size: " << standardMap.getTileSize() << std::endl;
        std::cout << "Number of paths: " << standardMap.getPathCount() << std::endl;
    } else {
        std::cout << "✗ Failed to load StandardMap!" << std::endl;
    }
    
    return 0;
}
