# 塔防游戏素材配置使用指南

## 📚 目录
1. [TilesheetConfig类使用](#tilesheetconfig类使用)
2. [GameData架构使用](#gamedata架构使用)
3. [敌人贴图系统](#敌人贴图系统)
4. [渲染系统实现](#渲染系统实现)
5. [词缀系统集成](#词缀系统集成)

## TilesheetConfig类使用

### 核心类实现
```cpp
// TilesheetConfig.h
#pragma once
#include <QPixmap>
#include <QRect>
#include <QPoint>
#include <QString>
#include <unordered_map>

class TilesheetConfig {
private:
    QPixmap tilesheet;
    int tileSize = 64;
    int gridWidth = 23;
    int gridHeight = 13;
    std::unordered_map<QString, int> tileIdMap;

    // 自动坐标计算核心函数
    QPoint calculatePosition(int tileId) const {
        if (tileId < 0 || tileId > 298) {
            qWarning() << "Invalid tile_id:" << tileId;
            return QPoint(0, 0);
        }
        int gridX = tileId % gridWidth;  // gridX = tileId % 23
        int gridY = tileId / gridWidth;  // gridY = tileId / 23
        return QPoint(gridX, gridY);
    }

    QRect calculatePixelRect(int tileId) const {
        QPoint gridPos = calculatePosition(tileId);
        return QRect(gridPos.x() * tileSize, gridPos.y() * tileSize, tileSize, tileSize);
    }

public:
    TilesheetConfig(const QString& tilesheetPath) {
        loadTilesheet(tilesheetPath);
        loadConfiguration();
    }

    // 主要接口：根据元素ID获取像素区域
    QRect getTileRect(const QString& elementId) const {
        auto it = tileIdMap.find(elementId);
        if (it == tileIdMap.end()) {
            qWarning() << "Element not found:" << elementId;
            return QRect(0, 0, tileSize, tileSize);
        }
        return calculatePixelRect(it->second);
    }

    // 根据tile_id直接获取像素区域
    QRect getTileRectById(int tileId) const {
        return calculatePixelRect(tileId);
    }

    // 获取瓦片的子图像
    QPixmap getTilePixmap(const QString& elementId) const {
        QRect rect = getTileRect(elementId);
        return tilesheet.copy(rect);
    }

    QPixmap getTilePixmapById(int tileId) const {
        QRect rect = getTileRectById(tileId);
        return tilesheet.copy(rect);
    }

private:
    void loadTilesheet(const QString& path) {
        if (!tilesheet.load(path)) {
            qWarning() << "Failed to load tilesheet:" << path;
        }
    }

    void loadConfiguration() {
        // 从配置文件加载，只关注tile_id字段
        tileIdMap["buildable_ground"] = 39;
        tileIdMap["path"] = 93;
        tileIdMap["start_point"] = 63;
        tileIdMap["end_point"] = 64;
        // UI元素
        for (int i = 0; i <= 9; ++i) {
            tileIdMap[QString("digit_%1").arg(i)] = 276 + i;
        }
        tileIdMap["dollar"] = 287;
        tileIdMap["colon"] = 288;
        tileIdMap["plus"] = 289;
    }
};
```

### 基础使用示例
```cpp
// 初始化
TilesheetConfig config("assets/Tilesheet/towerDefense_tilesheet.png");

// 渲染地图瓦片
void renderMapTile(QPainter& painter, const QRect& targetRect, const QString& tileType) {
    QPixmap tilePixmap = config.getTilePixmap(tileType);
    painter.drawPixmap(targetRect, tilePixmap);
}

// 渲染数字
void renderNumber(QPainter& painter, const QPoint& startPos, int number) {
    QString numberStr = QString::number(number);
    for (int i = 0; i < numberStr.length(); ++i) {
        int digit = numberStr[i].digitValue();
        QString digitId = QString("digit_%1").arg(digit);
        QPixmap digitPixmap = config.getTilePixmap(digitId);
        QPoint digitPos = startPos + QPoint(i * 64, 0);
        painter.drawPixmap(digitPos, digitPixmap);
    }
}

// 渲染障碍物（叠加渲染）
void renderObstacle(QPainter& painter, const QRect& targetRect) {
    // 1. 先渲染基础地面
    QPixmap groundPixmap = config.getTilePixmapById(24);
    painter.drawPixmap(targetRect, groundPixmap);

    // 2. 叠加物体
    QPixmap overlayPixmap = config.getTilePixmapById(130);
    painter.drawPixmap(targetRect, overlayPixmap);
}
```

## GameData架构使用

### 基础类型和数据获取
```cpp
#include "TowerDefense/Core/GameData.hpp"

// 获取塔的数据
void renderTower(QPainter& painter, const QRect& position, TowerType type) {
    const GameData& gameData = GameData::getInstance();

    // 获取塔的视觉数据
    const TowerVisualData& visualData = gameData.getVisualDataForTower(type);

    // 获取塔的平衡数据
    const UnitBalanceStats& stats = gameData.getStatsForTower(type);

    // 渲染塔基
    QPixmap basePixmap = tilesheetConfig->getTilePixmapById(visualData.baseTileId);
    painter.drawPixmap(position, basePixmap);

    // 渲染塔台
    QPixmap turretPixmap = tilesheetConfig->getTilePixmapById(visualData.turretTileId);
    painter.drawPixmap(position, turretPixmap);
}

// 远程塔的弹药系统
void renderRangedTower(QPainter& painter, const QRect& position, const RangedTower& tower) {
    const GameData& gameData = GameData::getInstance();
    const TowerVisualData& visualData = gameData.getVisualDataForTower(TowerType::RANGED);

    // 1. 渲染塔基
    QPixmap basePixmap = tilesheetConfig->getTilePixmapById(visualData.baseTileId);
    painter.drawPixmap(position, basePixmap);

    // 2. 根据弹药状态选择塔台
    int turretTileId = tower.hasAmmo ?
        visualData.turretLoadedTileId : visualData.turretEmptyTileId;
    QPixmap turretPixmap = tilesheetConfig->getTilePixmapById(turretTileId);
    painter.drawPixmap(position, turretPixmap);
}
```

## 敌人贴图系统

### 词缀组合自动选择
```cpp
// 基础敌人渲染
void renderEnemy(QPainter& painter, const QRect& position, const Enemy& enemy) {
    const GameData& gameData = GameData::getInstance();

    // 检查敌人词缀
    bool hasSwift = enemy.hasSwiftAffix();
    bool hasBlinking = enemy.hasBlinkingAffix();

    // 自动选择正确的贴图
    int enemyTileId = gameData.getEnemyTileIdByAffixes(
        enemy.getEnemyType(), hasSwift, hasBlinking
    );

    // 渲染敌人
    QPixmap enemyPixmap = tilesheetConfig->getTilePixmapById(enemyTileId);
    painter.drawPixmap(position, enemyPixmap);

    // 攻击效果
    if (enemy.isAttacking()) {
        int attackTileId = gameData.getEnemyAttackEffectTileId(enemy.getEnemyType());
        QPixmap attackPixmap = tilesheetConfig->getTilePixmapById(attackTileId);
        painter.drawPixmap(position, attackPixmap);
    }
}

// 手动贴图选择
void renderSpecificEnemyType(QPainter& painter, const QRect& position,
                           EnemyType type, bool swift, bool blinking) {
    const GameData& gameData = GameData::getInstance();
    const EnemyVisualData& visualData = gameData.getVisualDataForEnemy(type);

    int tileId;
    if (swift && blinking) {
        tileId = visualData.swiftBlinkingTileId;  // tile_id: 248
    } else if (swift) {
        tileId = visualData.swiftTileId;          // tile_id: 246
    } else if (blinking) {
        tileId = visualData.blinkingTileId;       // tile_id: 247
    } else {
        tileId = visualData.normalTileId;         // tile_id: 245
    }

    QPixmap enemyPixmap = tilesheetConfig->getTilePixmapById(tileId);
    painter.drawPixmap(position, enemyPixmap);
}
```

### 当前贴图配置
| 敌人状态  | Tile ID | 描述             |
| --------- | ------- | ---------------- |
| 普通敌人  | 245     | 无词缀的基础敌人 |
| 神速敌人  | 246     | 带SWIFT词缀      |
| 闪现敌人  | 247     | 带BLINKING词缀   |
| 神速+闪现 | 248     | 双词缀组合       |
| 攻击效果  | 295     | 敌人攻击特效     |

## 渲染系统实现

### GameRenderer类 - 主渲染器
```cpp
class GameRenderer {
private:
    TilesheetConfig* tilesheetConfig;

public:
    GameRenderer(TilesheetConfig* config) : tilesheetConfig(config) {}

    // 地图渲染
    void renderMapTile(QPainter& painter, const QRect& targetRect, const QString& tileType) {
        QPixmap tilePixmap = tilesheetConfig->getTilePixmap(tileType);
        painter.drawPixmap(targetRect, tilePixmap);
    }

    // 健康条渲染
    void renderHealthBar(QPainter& painter, const QRect& unitRect, float healthPercent) {
        QRect healthBarBg(unitRect.x(), unitRect.y() - 8, unitRect.width(), 4);
        QRect healthBarFill(healthBarBg.x(), healthBarBg.y(),
                           healthBarBg.width() * healthPercent, healthBarBg.height());

        painter.fillRect(healthBarBg, Qt::black);
        painter.fillRect(healthBarFill, healthPercent < 0.3 ? Qt::red : Qt::green);
    }

    // 攻击效果
    void renderAttackEffect(QPainter& painter, const QRect& targetRect) {
        painter.fillRect(targetRect, QColor(255, 255, 255, 180));
    }

    // 投射物渲染
    void renderProjectile(QPainter& painter, const QPoint& start, const QPoint& end, int tileId) {
        QPixmap projectilePixmap = tilesheetConfig->getTilePixmapById(tileId);
        // 计算投射物当前位置（这里简化为终点位置）
        painter.drawPixmap(end, projectilePixmap);
    }
};
```

### UIRenderer类 - UI专用渲染器
```cpp
class UIRenderer {
private:
    TilesheetConfig* tilesheetConfig;

public:
    UIRenderer(TilesheetConfig* config) : tilesheetConfig(config) {}

    // 渲染分数（带$符号）
    void renderScore(QPainter& painter, const QPoint& position, int score) {
        // 先绘制$符号
        QPixmap dollarPixmap = tilesheetConfig->getTilePixmap("dollar");
        painter.drawPixmap(position, dollarPixmap);

        // 再绘制分数数字
        QPoint numberPos = position + QPoint(64, 0);
        renderNumber(painter, numberPos, score);
    }

    // 渲染数字
    void renderNumber(QPainter& painter, const QPoint& startPos, int number) {
        QString numberStr = QString::number(number);
        for (int i = 0; i < numberStr.length(); ++i) {
            int digit = numberStr[i].digitValue();
            QString digitId = QString("digit_%1").arg(digit);
            QPixmap digitPixmap = tilesheetConfig->getTilePixmap(digitId);
            QPoint digitPos = startPos + QPoint(i * 64, 0);
            painter.drawPixmap(digitPos, digitPixmap);
        }
    }

    // 浮动伤害数字
    void renderFloatingDamage(QPainter& painter, const QPoint& pos, int damage, float timeElapsed) {
        painter.setPen(Qt::red);
        painter.setFont(QFont("Arial", 14, QFont::Bold));

        QPoint floatPos = pos + QPoint(0, -timeElapsed * 30);
        painter.setOpacity(1.0 - timeElapsed / 1.0);
        painter.drawText(floatPos, QString::number(damage));
        painter.setOpacity(1.0);
    }
};
```

## 词缀系统集成

### 词缀效果渲染
```cpp
class AffixRenderer {
public:
    // 塔词缀效果
    void renderTowerAffixes(QPainter& painter, const QRect& position, const Tower& tower) {
        for (const TowerAffix& affix : tower.affixes) {
            switch (affix) {
                case TowerAffix::BERSERK:
                    // 狂暴效果：红色光环
                    painter.setPen(QPen(Qt::red, 3));
                    painter.drawEllipse(position.adjusted(-5, -5, 5, 5));
                    break;

                case TowerAffix::ICE:
                    // 冰系效果：蓝色光环
                    painter.setPen(QPen(Qt::blue, 3));
                    painter.drawEllipse(position.adjusted(-5, -5, 5, 5));
                    break;

                case TowerAffix::AOE_SPLASH:
                    // 群伤效果：攻击范围指示
                    painter.setPen(QPen(Qt::yellow, 2, Qt::DashLine));
                    painter.drawEllipse(position.center(), tower.attackRange, tower.attackRange);
                    break;

                case TowerAffix::BLEEDING:
                    // 放血效果：红色拖尾
                    painter.setPen(QPen(Qt::darkRed, 2));
                    painter.drawEllipse(position.adjusted(-3, -3, 3, 3));
                    break;
            }
        }
    }

    // 敌人词缀效果
    void renderEnemyAffixes(QPainter& painter, const QRect& position, const Enemy& enemy) {
        for (const EnemyAffix& affix : enemy.affixes) {
            switch (affix) {
                case EnemyAffix::BLINKING:
                    // 闪现效果：透明度变化
                    if (enemy.isBlinking) {
                        float opacity = 0.3f + 0.7f * (sin(enemy.blinkTime * 15) + 1) / 2;
                        painter.setOpacity(opacity);
                    }
                    break;

                case EnemyAffix::SWIFT:
                    // 神速效果：拖尾线条
                    painter.setPen(QPen(Qt::cyan, 2));
                    painter.drawLine(enemy.lastPosition, position.center());
                    break;
            }
        }
    }
};
```

### 词缀数据管理
```cpp
class Tower {
public:
    TowerType type;
    std::vector<TowerAffix> affixes;

    // 根据词缀计算最终属性
    UnitBalanceStats getFinalStats() const {
        const GameData& gameData = GameData::getInstance();
        UnitBalanceStats baseStats = gameData.getStatsForTower(type);

        for (const TowerAffix& affix : affixes) {
            switch (affix) {
                case TowerAffix::BERSERK:
                    baseStats.attackDamage *= 2.0;      // 伤害翻倍
                    baseStats.attackInterval *= 0.5;    // 攻击间隔减半
                    break;
                case TowerAffix::ICE:
                    // 冰系词缀在攻击时添加冰冻效果，不改变基础属性
                    break;
                case TowerAffix::AOE_SPLASH:
                    // 群伤词缀在攻击时影响多个目标，不改变基础属性
                    break;
                case TowerAffix::BLEEDING:
                    // 放血词缀在攻击时添加持续伤害，不改变基础属性
                    break;
            }
        }
        return baseStats;
    }
};

class Enemy {
public:
    EnemyType type;
    std::vector<EnemyAffix> affixes;  // 最多两个词缀

    // 根据词缀计算最终属性
    UnitBalanceStats getFinalStats() const {
        const GameData& gameData = GameData::getInstance();
        UnitBalanceStats baseStats = gameData.getStatsForEnemy(type);

        for (const EnemyAffix& affix : affixes) {
            switch (affix) {
                case EnemyAffix::SWIFT:
                    baseStats.speed *= 1.5;  // 速度增加50%
                    break;
                case EnemyAffix::BLINKING:
                    // 闪现词缀提供特殊移动能力，不改变基础属性
                    break;
            }
        }
        return baseStats;
    }

    // 词缀检测方法
    bool hasSwiftAffix() const {
        return std::find(affixes.begin(), affixes.end(), EnemyAffix::SWIFT) != affixes.end();
    }

    bool hasBlinkingAffix() const {
        return std::find(affixes.begin(), affixes.end(), EnemyAffix::BLINKING) != affixes.end();
    }
};
```

## 集成使用示例

### 主游戏循环
```cpp
class GameWidget : public QWidget {
private:
    TilesheetConfig config;
    GameRenderer gameRenderer;
    UIRenderer uiRenderer;
    AffixRenderer affixRenderer;

public:
    GameWidget() :
        config("assets/Tilesheet/towerDefense_tilesheet.png"),
        gameRenderer(&config),
        uiRenderer(&config) {}

    void paintEvent(QPaintEvent* event) override {
        QPainter painter(this);

        // 1. 渲染地图
        gameRenderer.renderMapTile(painter, QRect(0, 0, 64, 64), "buildable_ground");
        gameRenderer.renderObstacle(painter, QRect(64, 0, 64, 64));

        // 2. 渲染游戏对象
        for (const auto& tower : towers) {
            gameRenderer.renderTower(painter, tower->rect, tower->type);
            affixRenderer.renderTowerAffixes(painter, tower->rect, *tower);
        }

        for (const auto& enemy : enemies) {
            gameRenderer.renderEnemy(painter, enemy->rect, *enemy);
            affixRenderer.renderEnemyAffixes(painter, enemy->rect, *enemy);
        }

        // 3. 渲染UI
        uiRenderer.renderScore(painter, QPoint(10, 10), gameState.gold);
        uiRenderer.renderNumber(painter, QPoint(200, 10), gameState.lives);
    }
};
```

---

这个使用指南涵盖了所有重要的使用场景和代码示例，可以作为开发的完整参考。