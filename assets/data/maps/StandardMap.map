# Tower Defense Map File - Standard Map
# 在这里写注释


[Metadata]
# 地图的名称
name = Square Loop

# 地图的宽度 (瓦片数)
width = 12

# 地图的高度 (瓦片数)
height = 10

# 每个瓦片在游戏世界中的尺寸 (像素或单位)
tileSize = 64.0

# 游戏初始设置 (可选)
initialLives = 20
initialGold = 500

# [Tile Legend]
# 定义瓦片字符与 TileType 枚举的映射
# 'G' : BUILDABLE_GROUND (远程塔可建造地面 - 远程塔可部署在这里，路径之外且与路径相邻)
# 'P' : PATH (通用路径瓦片 - 近战塔可部署在这里)
# 'S' : START_POINT (敌人出生点 - 路径的起始格子，近战塔可部署)
# 'E' : END_POINT (我方基地/目标点 - 路径的结束格子)
# 'W' : OBSTACLE (墙壁/障碍 - 不可通行，不可建造)

[Tiles]
# 地图瓦片布局 (width x height)
WWWWWWWWWWWW
WGGGGGGGGGWW
WGSPPPPPPPWW
WGPWWWWWGPWW
WGPWGGWWGPWW
WGPWGGWWGPWW
WGPWWWWWGPWW
WGPPPPPPPEWW
WGGGGGGGGGWW
WWWWWWWWWWWW

[Paths]
# 敌人行进路径，完整列出了从起点S到终点E的所有路径瓦片坐标
# 坐标系从左上角(0,0)开始
# 使用 Path:ID 表示一条新路径的开始

Path:0  # 外环 - 从 S 沿着上方和右侧到 E
2,2   # S - 起点
3,2   # P
4,2   # P
5,2   # P
6,2   # P
7,2   # P
8,2   # P
9,2   # P
9,3   # P
9,4   # P
9,5   # P
9,6   # P
9,7   # E - 终点

Path:1  # 内环 - 从 S 沿着左侧和下方到 E
2,2   # S - 起点
2,3   # P
2,4   # P
2,5   # P
2,6   # P
2,7   # P
3,7   # P
4,7   # P
5,7   # P
6,7   # P
7,7   # P
8,7   # P
9,7   # E - 终点

#注意：所有的路径加起来应当将所有的'P'和'S'和'E'占满