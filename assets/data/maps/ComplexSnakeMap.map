# Tower Defense Map File - Complex Snake Maze
# A larger, more intricate "snake" style map that winds through the playable area.

[Metadata]
# Map name
name = Snake Maze

# Map width (tiles)
width = 15

# Map height (tiles)
height = 10

# Size of each tile in the game world
# (Should match your engine settings)
tileSize = 64.0

# Initial game settings (optional)
initialLives = 20
initialGold = 500

# [Tile Legend]
# 'G' : BUILDABLE_GROUND  – Remote-tower buildable ground (not on path, adjacent to path)
# 'P' : PATH              – Common path tile (melee towers deployable here)
# 'S' : START_POINT       – Enemy spawn point (also a path tile, melee towers deployable)
# 'E' : END_POINT         – Our base / goal tile (path end)
# 'W' : OBSTACLE          – Impassable wall or obstacle (no movement, no building)

[Tiles]
# Map layout (width × height) - Simple snake pattern
WWWWWWWWWWWWWWW
WGGGGGGGGGGGGGW
WSPPPPPPPPPPPGW
WGGGGGGGGGGGPGW
WPPPPPPPPPPPPGW
WPPPPPPPPPPPPGW
WPPPPPPPPPPPPGW
WGGGGGGGGGGGPGW
WPPPPPPPPPPPPEW
WWWWWWWWWWWWWWW

[Paths]
# 敌人行进路径 - 简单蛇形通道
# Snake pattern: right -> down -> left -> down -> right -> end
Path:0
1,2   # S - start
2,2
3,2
4,2
5,2
6,2
7,2
8,2
9,2
10,2
11,2
12,2   # right to end of row 2
12,3   # down to row 3
12,4   # down to row 4
11,4
10,4
9,4
8,4
7,4
6,4
5,4
4,4
3,4
2,4
1,4    # left to start of row 4
1,5    # down to row 5
2,5
3,5
4,5
5,5
6,5
7,5
8,5
9,5
10,5
11,5
12,5   # right to end of row 5
12,6   # down to row 6
11,6
10,6
9,6
8,6
7,6
6,6
5,6
4,6
3,6
2,6
1,6    # left to start of row 6
2,6
3,6
4,6
5,6
6,6
7,6
8,6
9,6
10,6
11,6
12,6   # right to end of row 6
12,7   # down to row 7
12,8   # down to row 8
11,8
10,8
9,8
8,8
7,8
6,8
5,8
4,8
3,8
2,8
1,8    # left to start of row 8
2,8
3,8
4,8
5,8
6,8
7,8
8,8
9,8
10,8
11,8
12,8
13,8   # E - end