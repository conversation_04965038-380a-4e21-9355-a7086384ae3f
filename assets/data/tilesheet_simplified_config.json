{"metadata": {"version": "7.0", "description": "基础地图瓦片配置 - 游戏逻辑数据已移至GameData.cpp", "tilesheet_file": "assets/Tilesheet/towerDefense_tilesheet.png", "tilesheet_hd_file": "assets/Tilesheet/<EMAIL>", "tile_size": 64, "grid_width": 23, "grid_height": 13, "total_tiles": 299, "tile_id_range": "0 to 298", "coordinate_system": "只需填写tile_id，position自动计算", "note": "仅包含地图瓦片和UI元素，塔和敌人数据已移至GameData.cpp", "auto_calculation": {"position_formula": "gridX = tile_id % 23, gridY = tile_id / 23", "pixel_formula": "pixelX = gridX * 64, pixelY = gridY * 64"}}, "tile_mapping": {"description": "StandardMap.map中需要的瓦片类型映射", "required_tiles": {"G": "BUILDABLE_GROUND", "P": "PATH", "S": "START_POINT", "E": "END_POINT", "W": "OBSTACLE"}}, "tiles": {"description": "基础地形瓦片配置 - 只需填写tile_id", "tile_001": {"id": "buildable_ground", "tile_type": "BUILDABLE_GROUND", "map_char": "G", "description": "可建造地面 - 绿色方块", "tile_id": 39, "usage": "远程塔建造区域，路径相邻但不在路径上", "note": "position自动计算为[16,1]"}, "tile_002": {"id": "path", "tile_type": "PATH", "map_char": "P", "description": "路径瓦片 - 已配置", "tile_id": 93, "usage": "敌人行走路径，近战塔可建造", "note": "position自动计算为[1,4]"}, "tile_003": {"id": "obstacle", "tile_type": "OBSTACLE", "map_char": "W", "description": "障碍物 - 地面+物体叠加", "base_tile_id": 24, "overlay_tile_id": 130, "alternative_overlay_id": 134, "render_method": "layered", "usage": "不可通行，不可建造", "note": "基础地面 tile_id:24 [1,1] + 叠加物体 tile_id:130 [15,5]", "implementation": {"base_ground": "renderTile(painter, rect, 24)", "overlay_object": "renderTile(painter, rect, 130)", "alternative": "可选择tile_id:134作为叠加物体，概率相等"}}, "tile_004": {"id": "start_point", "tile_type": "START_POINT", "map_char": "S", "description": "敌人起点 - 独立瓦片", "tile_id": 63, "usage": "敌人生成位置，使用独立的起点图标", "note": "position自动计算为[17,2]"}, "tile_005": {"id": "end_point", "tile_type": "END_POINT", "map_char": "E", "description": "我方基地 - 独立瓦片", "tile_id": 64, "usage": "我方基地，敌人到达此处减少生命值", "note": "position自动计算为[18,2]"}}, "ui_elements": {"description": "UI数字和图标配置 - 只需填写tile_id", "ui_001": {"id": "digit_0", "tile_id": 276, "description": "数字0", "note": "position自动计算为[0,12]"}, "ui_002": {"id": "digit_1", "tile_id": 277, "description": "数字1", "note": "position自动计算为[1,12]"}, "ui_003": {"id": "digit_2", "tile_id": 278, "description": "数字2", "note": "position自动计算为[2,12]"}, "ui_004": {"id": "digit_3", "tile_id": 279, "description": "数字3", "note": "position自动计算为[3,12]"}, "ui_005": {"id": "digit_4", "tile_id": 280, "description": "数字4", "note": "position自动计算为[4,12]"}, "ui_006": {"id": "digit_5", "tile_id": 281, "description": "数字5", "note": "position自动计算为[5,12]"}, "ui_007": {"id": "digit_6", "tile_id": 282, "description": "数字6", "note": "position自动计算为[6,12]"}, "ui_008": {"id": "digit_7", "tile_id": 283, "description": "数字7", "note": "position自动计算为[7,12]"}, "ui_009": {"id": "digit_8", "tile_id": 284, "description": "数字8", "note": "position自动计算为[8,12]"}, "ui_010": {"id": "digit_9", "tile_id": 285, "description": "数字9", "note": "position自动计算为[9,12]"}, "ui_special": {"ui_011": {"id": "percent", "tile_id": 286, "description": "百分号%", "note": "position自动计算为[10,12]"}, "ui_012": {"id": "dollar", "tile_id": 287, "description": "美元符号$", "note": "position自动计算为[11,12]"}, "ui_013": {"id": "colon", "tile_id": 288, "description": "冒号:", "note": "position自动计算为[12,12]"}, "ui_014": {"id": "plus", "tile_id": 289, "description": "加号+", "note": "position自动计算为[13,12]"}, "ui_015": {"id": "dot", "tile_id": 290, "description": "点符号.", "note": "position自动计算为[14,12]"}}}, "coordinate_conversion": {"description": "自动坐标转换公式", "tile_id_to_grid": "gridX = tile_id % 23, gridY = Math.floor(tile_id / 23)", "grid_to_tile_id": "tile_id = gridY * 23 + gridX", "grid_to_pixel": "pixelX = gridX * 64, pixelY = gridY * 64", "pixel_to_grid": "gridX = Math.floor(pixelX / 64), gridY = Math.floor(pixelY / 64)", "max_tile_id": 298, "grid_bounds": "X: 0-22, Y: 0-12"}, "configuration_guide": {"step_1": "打开towerDefense_tilesheet.png (1472x832)", "step_2": "找到需要的瓦片/图标", "step_3": "记录tile_id (可以用：tile_id = 行号*23 + 列号)", "step_4": "只需修改对应ID的tile_id字段", "step_5": "position会自动计算，无需手动填写", "data_separation": {"map_tiles": "在此配置文件中配置 - 基础地形瓦片", "tower_data": "在GameData.cpp中配置 - 塔的所有属性和视觉数据", "enemy_data": "在GameData.cpp中配置 - 敌人的所有属性和视觉数据", "ui_elements": "在此配置文件中配置 - UI数字和图标"}, "simple_examples": {"左上角瓦片": "tile_id = 0", "第2行第1个瓦片": "tile_id = 23", "第3行第5个瓦片": "tile_id = 2*23+4 = 50", "右下角瓦片": "tile_id = 298"}, "quick_calculation": "tile_id = (行号-1) * 23 + (列号-1)，行号和列号从1开始计数"}}