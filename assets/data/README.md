# 塔防游戏素材配置系统

## 🎯 项目状态

**配置完整度：95%** - 所有基础游戏素材已配置完成，可立即开始游戏开发！

### ✅ 已完成配置
- **地图瓦片**: 5种基础地形全部配置完成
- **游戏数据**: 塔和敌人的所有属性和视觉数据完整
- **UI元素**: 数字0-9和符号已配置
- **类型系统**: 简化为基础类型+词缀系统，符合设计文档

### 📊 Tilesheet规格
- **图片尺寸**: 1472×832 像素 (23列×13行)
- **瓦片大小**: 64×64 像素
- **瓦片总数**: 299个 (ID: 0-298)
- **配置方式**: 只需填写`tile_id`，坐标自动计算

## 📁 文件结构

```
assets/data/
├── tilesheet_simplified_config.json  # 主配置文件
├── usage_guide.md                   # 使用指南
├── maps/
│   └── StandardMap.map              # 示例地图
└── README.md                        # 本文档
```

## 🚀 快速开始

### 1. 坐标计算系统
```cpp
// 核心转换公式（已集成到代码中）
int gridX = tile_id % 23;           // 列坐标 (0-22)
int gridY = tile_id / 23;           // 行坐标 (0-12)
int pixelX = gridX * 64;            // 像素X坐标
int pixelY = gridY * 64;            // 像素Y坐标
```

### 2. 基本使用
```cpp
#include "TilesheetConfig.h"

// 初始化
TilesheetConfig config("assets/Tilesheet/towerDefense_tilesheet.png");

// 获取瓦片区域
QRect tileRect = config.getTileRect("buildable_ground");  // 自动计算位置
QPixmap tilePixmap = config.getTilePixmap("path");        // 获取瓦片图像
```

### 3. 渲染示例
```cpp
// 渲染地图瓦片
QPixmap groundPixmap = config.getTilePixmap("buildable_ground");
painter.drawPixmap(targetRect, groundPixmap);

// 渲染UI数字
QPixmap digitPixmap = config.getTilePixmap("digit_5");
painter.drawPixmap(position, digitPixmap);
```

## 🎮 游戏元素配置

### 地图瓦片 (100%完成)
| 元素       | Tile ID | 网格位置     | 功能                     |
| ---------- | ------- | ------------ | ------------------------ |
| 可建造地面 | 39      | [16,1]       | 远程塔部署区域           |
| 路径       | 93      | [1,4]        | 敌人移动路径，近战塔部署 |
| 起点       | 63      | [17,2]       | 敌人生成点               |
| 终点       | 64      | [18,2]       | 敌人目标点               |
| 障碍物     | 24+130  | [1,1]+[15,5] | 装饰地形，叠加渲染       |

### 塔类型 (100%完成)
- **近战塔**: baseTileId(182), turretTileId(203), projectileTileId(297)
- **远程塔**: baseTileId(180), turretEmptyTileId(229), turretLoadedTileId(206), projectileTileId(252)

### 敌人类型 (100%完成)
- **普通敌人**: normalTileId(245)
- **神速敌人**: swiftTileId(246)
- **闪现敌人**: blinkingTileId(247)
- **神速+闪现**: swiftBlinkingTileId(248)
- **攻击效果**: attackEffectTileId(295)

### UI元素 (100%完成)
- **数字0-9**: tile_id 276-285
- **符号**: %$:+. tile_id 286-290

## 🔧 词缀系统

### 塔词缀
```cpp
enum class TowerAffix {
    BERSERK,     // 狂暴：伤害和攻击间隔大幅增加
    ICE,         // 冰系：攻击使敌人停止攻击和移动
    AOE_SPLASH,  // 群伤：同时攻击多个敌人
    BLEEDING     // 放血：附加流血效果，持续扣血
};
```

### 敌人词缀
```cpp
enum class EnemyAffix {
    BLINKING,    // 闪现：越过近战塔阻挡，有冷却时间
    SWIFT        // 神速：移动速度超过默认单位
};
```

## 📋 配置使用

### 地图字符映射
```cpp
// StandardMap.map中的瓦片映射
'G' : BUILDABLE_GROUND  // 可建造地面
'P' : PATH              // 路径瓦片
'S' : START_POINT       // 起点
'E' : END_POINT         // 终点
'W' : OBSTACLE          // 障碍物
```

### 基础渲染代码
```cpp
void renderMapTile(QPainter& painter, const QRect& targetRect, const QString& tileType) {
    QPixmap tilePixmap = tilesheetConfig->getTilePixmap(tileType);
    painter.drawPixmap(targetRect, tilePixmap);
}

void renderObstacle(QPainter& painter, const QRect& targetRect) {
    // 叠加渲染：地面 + 物体
    QPixmap groundPixmap = tilesheetConfig->getTilePixmapById(24);
    QPixmap overlayPixmap = tilesheetConfig->getTilePixmapById(130);
    painter.drawPixmap(targetRect, groundPixmap);
    painter.drawPixmap(targetRect, overlayPixmap);
}
```

## 🎯 下一步开发

### 优先级1: GUI组件实现
1. **GameViewWidget**: 核心游戏渲染组件
2. **InfoPanelWidget**: 资源和状态显示
3. **TowerPaletteWidget**: 塔选择面板

### 优先级2: 游戏循环集成
1. **定时器驱动**: 建立游戏更新循环
2. **信号槽连接**: 实现UI与逻辑交互
3. **基础玩法**: 塔放置、敌人移动、攻击系统

### 优先级3: 效果完善
1. **词缀效果**: 实现视觉反馈
2. **攻击动画**: 添加战斗特效
3. **UI完善**: 优化用户体验

## 📚 技术文档

详细的使用指南和代码示例请参考：
- `usage_guide.md` - 完整的API使用说明
- `tilesheet_simplified_config.json` - 主配置文件
- `maps/StandardMap.map` - 示例地图格式

## ✨ 核心优势

1. **零配置错误**: 自动坐标计算，避免手动计算错误
2. **简化开发**: 只需填写tile_id，无需管理坐标
3. **即用架构**: 所有基础数据已配置完成
4. **高度扩展**: 词缀系统支持无限组合
5. **性能优化**: 基础数据在内存中，词缀效果动态计算

---

**🎉 配置系统已完成，可立即开始游戏开发！**