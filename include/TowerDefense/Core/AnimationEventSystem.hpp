#pragma once

#include "TowerDefense/Types.hpp"

#include <QPointF>
#include <memory>
#include <vector>

// 动画事件类
class AnimationEvent {
   public:
    // 构造函数
    AnimationEvent(AnimationEventType eventType, UnitID id, QPointF pos, UnitType type)
        : m_type(eventType), m_unitId(id), m_position(pos), m_unitType(type) {}

    // 获取事件类型
    [[nodiscard]] AnimationEventType getType() const {
        return m_type;
    }
    // 获取单位ID
    [[nodiscard]] UnitID getUnitId() const {
        return m_unitId;
    }
    // 获取单位位置
    [[nodiscard]] QPointF getPosition() const {
        return m_position;
    }
    // 获取单位类型
    [[nodiscard]] UnitType getUnitType() const {
        return m_unitType;
    }
    // 获取目标单位ID
    [[nodiscard]] UnitID getTargetId() const {
        return m_targetId;
    }
    // 获取目标位置
    [[nodiscard]] QPointF getTargetPosition() const {
        return m_targetPosition;
    }
    // 获取伤害量
    [[nodiscard]] int getDamage() const {
        return m_damage;
    }
    // 获取剩余生命值
    [[nodiscard]] int getHealth() const {
        return m_health;
    }

    // 设置目标单位ID
    void setTargetId(UnitID id) {
        m_targetId = id;
    }
    // 设置目标位置
    void setTargetPosition(QPointF const& pos) {
        m_targetPosition = pos;
    }
    // 设置伤害量
    void setDamage(int dmg) {
        m_damage = dmg;
    }
    // 设置剩余生命值
    void setHealth(int hp) {
        m_health = hp;
    }

   private:
    AnimationEventType m_type;  // 事件类型
    UnitID m_unitId;            // 相关单位ID
    QPointF m_position;         // 事件位置
    UnitType m_unitType;        // 单位类型
    UnitID m_targetId = 0;      // 目标单位ID（如适用）
    QPointF m_targetPosition;   // 目标位置（如适用）
    int m_damage = 0;           // 伤害量（如适用）
    int m_health = 0;           // 剩余生命值（如适用）
};

// 观察者接口
class AnimationEventObserver {
   public:
    virtual ~AnimationEventObserver() = default;

    // 添加特殊成员函数声明
    AnimationEventObserver()                                         = default;
    AnimationEventObserver(AnimationEventObserver const&)            = default;
    AnimationEventObserver& operator=(AnimationEventObserver const&) = default;
    AnimationEventObserver(AnimationEventObserver&&)                 = default;
    AnimationEventObserver& operator=(AnimationEventObserver&&)      = default;

    // 事件处理方法
    virtual void onAnimationEvent(AnimationEvent const& event) = 0;
};

// 动画事件系统
class AnimationEventSystem {
   public:
    // 单例模式获取实例
    static AnimationEventSystem& getInstance();

    // 禁用拷贝和移动
    AnimationEventSystem(AnimationEventSystem const&)            = delete;
    AnimationEventSystem& operator=(AnimationEventSystem const&) = delete;
    AnimationEventSystem(AnimationEventSystem&&)                 = delete;
    AnimationEventSystem& operator=(AnimationEventSystem&&)      = delete;

    // 注册观察者
    void registerObserver(std::shared_ptr<AnimationEventObserver> const& observer);

    // 取消注册观察者
    void unregisterObserver(std::shared_ptr<AnimationEventObserver> const& observer);

    // 发送事件
    void sendEvent(AnimationEvent const& event);

    // 创建各种事件的辅助方法
    void notifyTowerBuilt(UnitID towerId, QPointF position, UnitType towerType);
    void notifyTowerDestroyed(UnitID towerId, QPointF position, UnitType towerType);
    void notifyEnemyKilled(UnitID enemyId, QPointF position, UnitType enemyType);
    void notifyEnemyEscaped(UnitID enemyId, QPointF position, UnitType enemyType);
    void notifyEnemySpawned(UnitID enemyId, QPointF position, UnitType enemyType);
    void notifyTowerAttack(
        UnitID towerId, QPointF towerPos, UnitID targetId, QPointF targetPos, int damage, UnitType towerType);
    void notifyEnemyDamageTaken(UnitID enemyId, QPointF position, int damage, int remainingHealth, UnitType enemyType);

   private:
    // 单例相关
    AnimationEventSystem()  = default;
    ~AnimationEventSystem() = default;

    // 观察者列表
    std::vector<std::shared_ptr<AnimationEventObserver>> m_observers;
};
