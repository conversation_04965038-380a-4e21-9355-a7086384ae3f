#pragma once

#include "TowerDefense/Types.hpp"  // 包含UnitType, TowerType, EnemyType等

#include <unordered_map>

// 存储游戏平衡数据的结构体
struct UnitBalanceStats {
    // 经济相关
    int buildCost;      // 建造塔的花费
    int sellValue;      // 出售塔的价值 (如果游戏有这个功能)
    int killReward;     // 杀死敌人获得的金币/分数
    int escapePenalty;  // 敌人逃脱扣除的生命值/分数

    // 单位属性
    int maxHealth;  // 最大生命值
    float speed;    // 移动速度（塔为0）

    // 战斗属性
    int attackDamage;      // 攻击伤害
    float attackRange;     // 攻击范围
    float attackInterval;  // 攻击间隔（秒）
};

// 塔的视觉组件数据结构
struct TowerVisualData {
    int baseTileId;          // 塔基tile_id
    int turretTileId;        // 塔台tile_id (近战塔使用)
    int turretEmptyTileId;   // 无弹药塔台tile_id (远程塔使用)
    int turretLoadedTileId;  // 装弹药塔台tile_id (远程塔使用)
    int projectileTileId;    // 投射物tile_id
    bool hasAmmoSystem;      // 是否有弹药系统
    bool rotationSupport;    // 是否支持旋转
};

// 敌人的视觉组件数据结构
struct EnemyVisualData {
    // 基础敌人贴图（不同词缀组合）
    int normalTileId;         // 普通敌人（无词缀）
    int swiftTileId;          // 神速敌人（SWIFT词缀）
    int blinkingTileId;       // 闪现敌人（BLINKING词缀）
    int swiftBlinkingTileId;  // 神速+闪现敌人（SWIFT + BLINKING词缀）

    // 攻击效果贴图
    int attackEffectTileId;  // 敌人攻击效果贴图
};

// 集中管理所有游戏平衡数据的类 (可以设计为单例或通过依赖注入传递)
class GameData {
   public:
    // 获取单例实例 (如果使用单例模式)
    static GameData const& getInstance();

    // 根据塔类型获取其统计数据
    [[nodiscard]] UnitBalanceStats const& getStatsForTower(TowerType type) const;

    // 根据敌人类型获取其统计数据
    [[nodiscard]] UnitBalanceStats const& getStatsForEnemy(EnemyType type) const;

    // 根据塔类型获取其视觉数据
    [[nodiscard]] TowerVisualData const& getVisualDataForTower(TowerType type) const;

    // 根据敌人类型获取其视觉数据
    [[nodiscard]] EnemyVisualData const& getVisualDataForEnemy(EnemyType type) const;

    // 根据敌人的词缀组合获取对应的贴图ID
    [[nodiscard]] int getEnemyTileIdByAffixes(EnemyType type, bool hasSwift, bool hasBlinking) const;

    // 获取敌人攻击效果贴图ID
    [[nodiscard]] int getEnemyAttackEffectTileId(EnemyType type) const;

    // 获取初始生命值
    [[nodiscard]] int getInitialLives() const {
        return m_initialLives;
    }

    // 获取初始金币数量
    [[nodiscard]] int getInitialMoney() const {
        return m_initialMoney;
    }

    // 防止复制、移动和赋值
    GameData(GameData const&)            = delete;
    GameData& operator=(GameData const&) = delete;
    GameData(GameData&&)                 = delete;
    GameData& operator=(GameData&&)      = delete;

    // 析构函数
    ~GameData() = default;

   private:
    // 构造函数私有化，强制使用单例 (如果使用单例模式)
    GameData();

    // 存储不同塔类型的统计数据
    std::unordered_map<TowerType, UnitBalanceStats> m_towerStats;
    // 存储不同敌人类型的统计数据
    std::unordered_map<EnemyType, UnitBalanceStats> m_enemyStats;

    // 存储不同塔类型的视觉数据
    std::unordered_map<TowerType, TowerVisualData> m_towerVisualData;
    // 存储不同敌人类型的视觉数据
    std::unordered_map<EnemyType, EnemyVisualData> m_enemyVisualData;

    // 游戏初始配置
    int m_initialLives;  // 初始生命值
    int m_initialMoney;  // 初始金币数量
};
