#pragma once

#include "TowerDefense/Types.hpp"

#include <memory>
#include <string>
#include <vector>

#include <QtCore/QObject>
#include <QtCore/QPoint>

// 前向声明
class Map;
class UnitManager;
class WaveManager;
class ResourceManager;
class Unit;
class Tower;
class Enemy;
class AnimationEvent;
class AnimationEventObserver;

// 在 GameController 类的声明之前添加用于存储已放置塔的信息结构体
struct PlacedTowerInfo {
    TowerType type;
    QPoint gridPos;
};

// 游戏控制器 - 继承QObject以支持信号槽
class GameController : public QObject {
    Q_OBJECT

   public:
    // 单例模式
    static GameController& getInstance();

    // 主要公共接口
    void startGame(std::string const& mapFile);                     // 开始游戏
    void pauseGame();                                               // 暂停游戏
    void resumeGame();                                              // 恢复游戏
    void quitGame();                                                // 退出游戏
    void nextWave();                                                // 下一波
    void placeTower(int towerType, float x, float y);               // 放置塔
    bool requestPlaceTower(TowerType type, QPoint const& gridPos);  // 请求放置塔（返回是否成功）
    void setMap(std::shared_ptr<Map> map);                          // 设置当前地图并初始化相关管理器
    std::shared_ptr<Map> getCurrentMap() const;                     // 获取当前地图
    std::vector<PlacedTowerInfo> const& getPlacedTowers() const;    // 获取已放置的塔列表

    // 处理单位死亡和逃脱
    void processEnemyDeath(Enemy* enemy);  // 处理敌人死亡
    void processTowerDeath(Tower* tower);  // 处理塔死亡
    void loseLives(int amount);            // 扣除生命值
    void addCoins(int amount);             // 增加金币

    // 处理动画事件
    void handleAnimationEvent(AnimationEvent const& event);

    // 获取当前状态
    [[nodiscard]] GameState getGameState() const;
    [[nodiscard]] int getCurrentMoney() const;
    [[nodiscard]] int getCurrentLives() const;
    [[nodiscard]] int getCurrentWave() const;
    [[nodiscard]] int getTotalWaves() const;

    // 每帧逻辑更新，由UI层或主循环调用
    void tick(float deltaTime);

    // 获取所有活跃敌人（只读副本，用于渲染）
    std::vector<std::shared_ptr<Unit>> getAllEnemies() const;

    // 获取所有活跃塔（只读副本，用于渲染）
    std::vector<std::shared_ptr<Unit>> getAllTowers() const;

    // 禁用拷贝和移动
    GameController(GameController const&)            = delete;
    GameController& operator=(GameController const&) = delete;
    GameController(GameController&&)                 = delete;
    GameController& operator=(GameController&&)      = delete;

   signals:
    // GUI更新信号
    void gameStateChanged(GameState newState);
    void moneyChanged(int newAmount, int change);
    void livesChanged(int newLives, int change);
    void waveChanged(int currentWave, int totalWaves);
    void waveCountdownChanged(int secondsRemaining);

    // 单位相关信号
    void towerPlaced(Tower* tower);
    void towerDestroyed(Tower* tower);
    void enemySpawned(Enemy* enemy);
    void enemyKilled(Enemy* enemy);
    void enemyEscaped(Enemy* enemy);

    // 战斗效果信号
    void attackPerformed(Unit* attacker, Unit* target, int damage);
    void unitHealthChanged(Unit* unit, int newHealth, int maxHealth);

   public slots:
    // GUI触发的槽函数
    void onStartWaveRequested();
    void onPauseToggled();
    void onTowerPlaceRequested(TowerType type, QPoint gridPos);

   private:
    // 单例相关
    GameController();
    ~GameController() override;

    // 游戏状态
    int m_lives;        // 玩家生命值
    int m_currentWave;  // 当前波次
    int m_totalWaves;   // 总波次数

    // 私有成员变量
    GameState m_currentState;
    std::shared_ptr<Map> m_currentMap;
    std::unique_ptr<UnitManager> m_unitManager;
    std::unique_ptr<WaveManager> m_waveManager;
    std::unique_ptr<ResourceManager> m_resourceManager;

    // 动画状态
    std::shared_ptr<AnimationEventObserver> m_animationObserver;  // 动画事件观察者

    std::vector<PlacedTowerInfo> m_placedTowers;  // 存储玩家已放置的塔

    // 关波倒计时相关
    float m_interwaveCountdown    = -1.0f;  // 剩余时间，<0 表示无倒计时
    float m_interwaveCountdownMax = 0.0f;   // 当前倒计时的最大值，用于计算奖励

    // 私有方法
    void updateGame(float deltaTime);
    void checkWinLossConditions();
    void loadLevelData(std::string const& mapFile);
    void saveGame();
    void loadGame();
    void handleMouseClick(int x, int y);
    void handleMouseMove(int x, int y);
    void handleMouseRelease(int x, int y);
    void handleKeyPress(int key);
    void handleKeyRelease(int key);
    void handleWindowResize(int width, int height);

    // 注册为动画事件观察者
    void registerForAnimationEvents();
    // 取消注册动画事件观察者
    void unregisterFromAnimationEvents();

    // 内部状态更新方法
    void updateMoney(int newAmount);
    void updateLives(int newLives);
    void updateWave(int newWave);
    void updateGameState(GameState newState);
};