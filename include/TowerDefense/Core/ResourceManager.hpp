#pragma once

#include <cstdint>

// ResourceManager focuses on data management,
// GameController will handle emitting signals when data changes.

// 后续可以使用 ResourcesType 枚举来管理多种资源
// #include "TowerDefense/Types.h"

class ResourceManager {
   public:
    ResourceManager() = default;

    std::uint32_t getMoney() const;

    bool addMoney(std::uint32_t amount);

    bool subtractMoney(std::uint32_t amount);

    void setMoney(std::uint32_t amount);

    // Phase 2 或之后可能添加的方法来管理其他资源
    // bool addResource(ResourceType type, std::uint32_t amount);
    // bool subtractResource(ResourceType type, std::uint32_t amount);
    // std::uint32_t getResource(ResourceType type) const;

   private:
    std::uint32_t m_money = 0;

    // 存储其他资源（如果使用多种资源）
    // std::map<ResourceType, std::uint32_t> m_resources;
};