#pragma once

#include "TowerDefense/Types.hpp"

#include <QGroupBox>
#include <QLabel>
#include <QListWidget>
#include <QPushButton>
#include <QVBoxLayout>
#include <QWidget>
#include <vector>

// 前向声明
class Tower;

class AffixManagementWidget : public QWidget {
    Q_OBJECT

   public:
    explicit AffixManagementWidget(QWidget* parent = nullptr);

   public slots:
    void setSelectedTower(Tower* tower);
    void updateAffixLibrary(std::vector<TowerAffix> const& availableAffixes);

   signals:
    void affixEquipRequested(Tower* tower, TowerAffix affix, int slotIndex);
    void affixUnequipRequested(Tower* tower, int slotIndex);
    void showAffixLibraryRequested();

   private slots:
    void onShowLibraryClicked();
    void onEquipAffixClicked();
    void onUnequipSlot1Clicked();
    void onUnequipSlot2Clicked();

   private:
    void setupUI();
    void updateDisplay();
    QString getAffixName(TowerAffix affix) const;

   private:
    QGroupBox* m_groupBox;
    QPushButton* m_showLibraryButton;
    QPushButton* m_equipAffixButton;

    // 词缀槽显示
    QLabel* m_slot1Label;
    QLabel* m_slot2Label;
    QPushButton* m_unequipSlot1Button;
    QPushButton* m_unequipSlot2Button;

    Tower* m_selectedTower = nullptr;
    std::vector<TowerAffix> m_availableAffixes;
};