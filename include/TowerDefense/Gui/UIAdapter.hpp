#pragma once

#include "TowerDefense/Types.hpp"

#include <QObject>

// 前向声明
class GameController;
class MainWindow;
class GameInfoWidget;
class TowerSelectionWidget;
class GameViewWidget;
class UnitInfoWidget;
class AffixManagementWidget;

/**
 * @brief UI适配器类，负责连接GameController和GUI组件
 *
 * 这个类将GameController的信号连接到相应的GUI组件槽函数，
 * 同时将GUI组件的信号连接到GameController的槽函数。
 */
class UIAdapter : public QObject {
    Q_OBJECT

   public:
    explicit UIAdapter(GameController* gameController, MainWindow* mainWindow, QObject* parent = nullptr);

    /**
     * @brief 建立所有的信号槽连接
     */
    void setupConnections();

   private slots:
    // 处理GameController信号的槽函数
    void onGameStateChanged(GameState newState);
    void onMoneyChanged(int newAmount, int change);
    void onLivesChanged(int newLives, int change);
    void onWaveChanged(int currentWave, int totalWaves);
    void onWaveCountdownChanged(int secondsRemaining);

    // Handle GameInfoWidget start wave button signal
    void onStartWaveButtonClicked();

    // 处理GUI组件信号的槽函数
    void onTowerSelectionRequested(TowerType type);
    void onTowerPlacementRequested(TowerType type, QPoint const& gridPos);

   private:
    GameController* m_gameController;
    MainWindow* m_mainWindow;

    // GUI组件引用
    GameInfoWidget* m_gameInfoWidget;
    TowerSelectionWidget* m_towerSelectionWidget;
    GameViewWidget* m_gameViewWidget;
    UnitInfoWidget* m_unitInfoWidget;
    AffixManagementWidget* m_affixManagementWidget;

    /**
     * @brief 获取MainWindow中的GUI组件引用
     */
    void obtainWidgetReferences();

    /**
     * @brief 连接GameController信号到GUI组件槽
     */
    void connectControllerToGui();

    /**
     * @brief 连接GUI组件信号到GameController槽
     */
    void connectGuiToController();

    /**
     * @brief 连接GUI组件之间的信号槽
     */
    void connectGuiComponents();

    /**
     * @brief 初始化GUI组件的初始状态
     */
    void initializeGuiState();
};