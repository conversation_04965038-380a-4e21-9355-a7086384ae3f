#pragma once

#include <QGroupBox>
#include <QLabel>
#include <QVBoxLayout>
#include <QWidget>

class GameInfoWidget : public QWidget {
    Q_OBJECT

   public:
    explicit GameInfoWidget(QWidget* parent = nullptr);

   public slots:
    void updateMoney(int amount);
    void updateLives(int lives);
    void updateCurrentWave(int wave);

   private:
    void setupUI();

   private:
    QGroupBox* m_groupBox;
    QLabel* m_moneyLabel;
    QLabel* m_livesLabel;
    QLabel* m_waveLabel;

    int m_currentMoney = 500;  // 初始金钱
    int m_currentLives = 20;   // 初始生命
    int m_currentWave  = 1;    // 当前波次
};