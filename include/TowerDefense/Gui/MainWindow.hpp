#pragma once

#include "TowerDefense/Types.hpp"

#include <QAction>
#include <QComboBox>
#include <QDir>
#include <QFileDialog>
#include <QGroupBox>
#include <QMainWindow>  // 或者 #include <QWidget>
#include <QMenu>
#include <QMenuBar>
#include <QMessageBox>
#include <QObject>  // Q_OBJECT 宏需要
#include <QPushButton>
#include <QScrollArea>
#include <memory>  // 用于 std::unique_ptr

// 前向声明GUI控件类
class GameViewWidget;
class GameInfoWidget;
class TowerSelectionWidget;
class UnitInfoWidget;
class AffixManagementWidget;
class UIAdapter;
class GameController;
class TileRenderer;
class Map;

class MainWindow : public QMainWindow  // 或者 public QWidget
{
   Q_OBJECT  // 启用Qt的元对象系统

       public :
       // 构造函数
       explicit MainWindow(QWidget* parent = nullptr);

    // 析构函数
    ~MainWindow() override;

    // 获取GUI组件的访问器方法
    GameViewWidget* getGameViewWidget() const;
    GameInfoWidget* getGameInfoWidget() const;
    TowerSelectionWidget* getTowerSelectionWidget() const;
    UnitInfoWidget* getUnitInfoWidget() const;
    AffixManagementWidget* getAffixManagementWidget() const;

    // 初始化UI适配器
    void initializeUIAdapter();

    // 设置渲染系统
    void setTileRenderer(std::shared_ptr<TileRenderer> tileRenderer);
    void setMap(std::shared_ptr<Map> map);

   public slots:
    // 声明槽函数，用于响应GameController或其他游戏逻辑发出的信号
    void handleGameStateChanged(GameState newState);                // 响应游戏状态变化
    void handleLivesChanged(int newLives, int amountChanged);       // 响应生命值变化
    void handleMoneyChanged(uint32_t newMoney, int amountChanged);  // 响应金币变化
                                                                    // 添加其他需要的槽函数
    // 新增: 地图菜单槽函数
    void onImportMap();
    void onExportMap();
    void onBuiltinMapSelected();
    void onBuiltinMapComboChanged(int index);

   private:
    // 设置UI界面
    void setupUi();

    // 创建GUI组件
    void createWidgets();
    void createMenus();
    void loadMapFromPath(QString const& filePath);

    // 成员变量，指向窗口中的各个子控件
    GameViewWidget* m_gameView;                // 游戏区域控件
    GameInfoWidget* m_gameInfo;                // 游戏信息面板控件
    TowerSelectionWidget* m_towerSelection;    // 塔选择控件
    UnitInfoWidget* m_unitInfo;                // 单位信息控件
    AffixManagementWidget* m_affixManagement;  // 词缀管理控件

    // UI适配器，负责连接后端和前端
    std::unique_ptr<UIAdapter> m_uiAdapter;

    // 菜单相关
    QMenuBar* m_menuBar         = nullptr;
    QMenu* m_mapMenu            = nullptr;
    QMenu* m_builtinMapsSubMenu = nullptr;

    // 地图管理面板控件
    QPushButton* m_importMapButton = nullptr;
    QPushButton* m_exportMapButton = nullptr;
    QComboBox* m_builtinMapCombo   = nullptr;

    // 包裹游戏视图的滚动区域
    QScrollArea* m_gameScrollArea = nullptr;

    // 如果需要直接处理输入事件，可以重写以下函数
    // void mousePressEvent(QMouseEvent *event) override;
    // void keyPressEvent(QKeyEvent *event) override;
    // void resizeEvent(QResizeEvent *event) override;
};
