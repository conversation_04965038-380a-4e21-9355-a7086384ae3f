#pragma once

#include "TowerDefense/Types.hpp"

#include <QGroupBox>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QWidget>

class TowerSelectionWidget : public QWidget {
    Q_OBJECT

   public:
    explicit TowerSelectionWidget(QWidget* parent = nullptr);

    void updateAvailableMoney(int money);

   signals:
    void towerTypeSelected(TowerType type);

   protected:
    void keyPressEvent(QKeyEvent* event) override;

   private slots:
    void onMeleeTowerClicked();
    void onRangedTowerClicked();

   private:
    void setupUI();
    void updateButtonStates();

   private:
    QGroupBox* m_groupBox;
    QPushButton* m_meleeTowerButton;
    QPushButton* m_rangedTowerButton;
    QLabel* m_meleeCostLabel;
    QLabel* m_rangedCostLabel;

    int m_currentMoney = 0;

    // 简化的成本设定
    static constexpr int MELEE_TOWER_COST  = 100;
    static constexpr int RANGED_TOWER_COST = 150;
};