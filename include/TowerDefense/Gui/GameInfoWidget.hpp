#pragma once

#include <QGroupBox>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QWidget>

class GameInfoWidget : public QWidget {
    Q_OBJECT

   public:
    explicit GameInfoWidget(QWidget* parent = nullptr);

   public slots:
    void updateMoney(int amount);
    void updateLives(int lives);
    void updateWaveProgress(int currentWave, int totalWaves);
    void updateCountdown(int secondsRemaining);

   signals:
    void startWaveClicked();

   public:
    // Enable or disable the start wave button (and optionally show/hide it)
    void setStartWaveButtonEnabled(bool enabled);

   private:
    void setupUI();

   private:
    QGroupBox* m_groupBox;
    QLabel* m_moneyLabel;
    QLabel* m_livesLabel;
    QLabel* m_waveLabel;
    QLabel* m_countdownLabel;
    QPushButton* m_startWaveButton;

    int m_currentMoney = 500;  // 初始金钱
    int m_currentLives = 20;   // 初始生命
    int m_currentWave  = 0;
    int m_totalWaves   = 0;
    int m_countdownSec = -1;
};