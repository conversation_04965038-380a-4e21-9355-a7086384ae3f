#pragma once

#include <QGroupBox>
#include <QLabel>
#include <QVBoxLayout>
#include <QWidget>

// 前向声明
class Unit;
class Tower;
class Enemy;

class UnitInfoWidget : public QWidget {
    Q_OBJECT

   public:
    explicit UnitInfoWidget(QWidget* parent = nullptr);

   public slots:
    void showUnitInfo(Unit* unit);
    void clearUnitInfo();

   private:
    void setupUI();
    void showTowerInfo(Tower const* tower);
    void showEnemyInfo(Enemy const* enemy);

   private:
    QGroupBox* m_groupBox;
    QLabel* m_unitTypeLabel;
    QLabel* m_healthLabel;
    QLabel* m_damageLabel;
    QLabel* m_rangeLabel;
    QLabel* m_affixLabel;

    Unit* m_currentUnit = nullptr;
};