#pragma once

#include "TowerDefense/Types.hpp"

#include <QMenu>
#include <QMouseEvent>
#include <QPainter>
#include <QTimer>
#include <QWidget>
#include <memory>
#include <optional>
#include <vector>

// 前向声明
class GameController;
class Unit;
class Tower;
class Enemy;
class TilesheetConfig;
class TileRenderer;
class Map;

class GameViewWidget : public QWidget {
    Q_OBJECT

   public:
    explicit GameViewWidget(QWidget* parent = nullptr);
    ~GameViewWidget() override;

    // 设置游戏控制器
    void setGameController(GameController* controller);

    // 设置地图和渲染器
    void setMap(std::shared_ptr<Map> map);
    void setTileRenderer(std::shared_ptr<TileRenderer> tileRenderer);

   protected:
    // Qt事件处理
    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void keyPressEvent(QKeyEvent* event) override;

   signals:
    void tileClicked(QPoint const& gridPos, Qt::MouseButton button);
    void unitSelected(Unit* unit);
    void placeTowerRequested(TowerType type, QPoint const& gridPos);

   public slots:
    void onGameUpdated();
    void setTowerPlacementMode(TowerType type);
    void clearTowerPlacementMode();

    // 接收游戏控制器发出的战斗效果信号，在界面上播放简易动画
    void onAttackPerformed(Unit* attacker, Unit* target, int damage);

   private:
    // 简化的渲染方法
    void drawBackground(QPainter& painter);
    void drawGrid(QPainter& painter);
    void drawPaths(QPainter& painter);
    void drawUnits(QPainter& painter);
    void drawEffects(QPainter& painter);

    // 坐标转换
    QPoint screenToGrid(QPointF const& screenPos) const;
    QPointF gridToScreen(QPoint const& gridPos) const;

    // 简化的单位渲染
    void drawTower(QPainter& painter, Tower const* tower);
    void drawEnemy(QPainter& painter, Enemy const* enemy);
    void drawSimpleHealthBar(QPainter& painter, Unit const* unit);
    void drawAffixIndicators(QPainter& painter, Unit const* unit);

    // 辅助方法
    Unit* findUnitAt(QPoint const& gridPos) const;
    bool canPlaceTowerAt(QPoint const& gridPos, TowerType type) const;
    void showTowerSelectionMenu(QPoint const& globalPos, QPoint const& gridPos);

   private:
    // 渲染常量
    static constexpr int TILE_SIZE = 64;  // 使用配置文件中的瓦片大小

    // 核心引用
    GameController* m_gameController = nullptr;
    QTimer* m_updateTimer;

    // 地图和渲染系统
    std::shared_ptr<Map> m_map;
    std::shared_ptr<TileRenderer> m_tileRenderer;

    // 简化的状态变量
    Unit* m_selectedUnit                        = nullptr;
    std::optional<TowerType> m_placingTowerType = std::nullopt;

    // 简化的效果系统
    struct SimpleAttackEffect {
        QPointF startPos, endPos;
        float progress;
        QColor color;
        int projectileTileId = -1;  // -1 表示使用备用线条效果

        // 如果目标会被此次攻击击杀，则在弹道飞行过程中保留其贴图直至命中
        bool targetWillDie = false;
        int targetTileId   = -1;
        QPointF targetLastPos;  // 记录击杀瞬间位置
    };
    std::vector<SimpleAttackEffect> m_attackEffects;

    struct FlashEffect {
        QPointF position;
        int remainingFrames;
    };
    std::vector<FlashEffect> m_flashPositions;

    // 上一次逻辑更新时间（毫秒）
    qint64 m_lastLogicTimeMs = 0;
};