#pragma once

#include "TowerDefense/Types.hpp"

#include <memory>
#include <vector>

// 前向声明
class Map;
class UnitManager;

// 一波敌人的描述
struct WaveData {
    std::vector<EnemyType> enemies;  // 这波敌人的类型
    float spawnInterval;             // 生成间隔（秒）
    int pathIndex;                   // 生成路径索引
};

// 波次管理器，负责生成敌人波次
class WaveManager {
   public:
    // 构造函数
    WaveManager(Map const& map, UnitManager& unitManager);

    // 更新函数，由 GameController 在游戏循环中调用
    void update(float deltaTime);

    // 开始下一波敌人
    void startNextWave();

    // 检查是否所有波次都已完成
    [[nodiscard]] bool isAllWavesCompleted() const;

    // 获取当前波次索引
    [[nodiscard]] int getCurrentWaveIndex() const;

    // 获取总波次数
    [[nodiscard]] int getTotalWaves() const;

    // 查询当前是否有波次正在进行
    [[nodiscard]] bool isWaveInProgress() const;

   private:
    Map const& m_map;            // 引用地图
    UnitManager& m_unitManager;  // 引用单位管理器

    std::vector<WaveData> m_waves;  // 所有波次数据
    int m_currentWaveIndex;         // 当前波次索引

    bool m_waveInProgress;       // 是否有波次正在进行
    float m_timeSinceLastSpawn;  // 距离上次生成敌人的时间
    size_t m_enemiesSpawned;     // 当前波次已生成的敌人数量

    // 加载波次数据
    void loadWaveData();

    // 生成敌人
    void spawnEnemy(EnemyType type, int pathIndex);
};