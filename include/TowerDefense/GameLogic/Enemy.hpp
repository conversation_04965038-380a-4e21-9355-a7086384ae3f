#pragma once

#include "TowerDefense/GameLogic/Unit.hpp"
#include "TowerDefense/Types.hpp"

#include <QPointF>
#include <memory>
#include <vector>

// 前向声明
class Map;
class Affix;

// 敌人基类
class Enemy : public Unit {
   public:
    ~Enemy() override = default;

    // 更新敌人的状态
    void update(float deltaTime) override;

    // 检查是否死亡
    [[nodiscard]] bool isDead() const override;

    // 检查是否逃跑（到达终点）
    [[nodiscard]] bool hasEscaped() const override;

    // 获取当前路径索引
    [[nodiscard]] int getPathIndex() const {
        return m_pathIndex;
    }

    // 获取在路径上的进度
    [[nodiscard]] float getPathProgress() const {
        return m_pathProgress;
    }

    // 获取敌人类型
    [[nodiscard]] EnemyType getEnemyType() const {
        return m_enemyType;
    }

    // 添加词缀
    bool addAffix(std::shared_ptr<Affix> const& affix);

    // 移除词缀
    bool removeAffix(unsigned int affixIndex);

    // 获取当前词缀
    [[nodiscard]] std::vector<std::shared_ptr<Affix>> const& getAffixes() const;

    // 获取奖励金币
    [[nodiscard]] virtual int getRewardCoins() const = 0;

    // 获取逃脱惩罚
    [[nodiscard]] virtual int getEscapePenalty() const = 0;

    // 获取朝向角度（度）
    [[nodiscard]] float getRotation() const {
        return m_rotation;
    }

    // 获取当前航点索引
    [[nodiscard]] int getWaypointIndex() const {
        return m_waypointIndex;
    }

   protected:
    // 构造函数
    Enemy(UnitID id,
          EnemyType enemyType,
          UnitType unitType,
          Map const& map,
          int pathIndex,      // 敌人在地图上的路径索引
          int initialHealth,  // 敌人的初始生命值
          float moveSpeed,    // 敌人的移动速度
          int killReward);    // 击败敌人的金币奖励

    EnemyType m_enemyType;                          // 敌人类型
    Map const& m_map;                               // 地图引用
    int m_pathIndex;                                // 路径索引
    float m_pathProgress{0.0F};                     // 路径进度（0.0-1.0）
    int m_waypointIndex{0};                         // 当前航点索引
    bool m_escaped{false};                          // 是否已逃跑
    std::vector<std::shared_ptr<Affix>> m_affixes;  // 词缀
    static constexpr int MAX_AFFIXES = 2;           // 最大词缀数量
    int m_rewardCoins;                              // 击败后奖励的金币
    float m_rotation{0.0F};                         // 朝向角度（度）

    // 更新敌人在路径上的位置
    void updateMovement(float deltaTime);

    // 检测与近战塔的碰撞
    bool checkCollision(std::vector<std::shared_ptr<Unit>> const& units);
};

// 普通敌人
class NormalEnemy : public Enemy {
   public:
    NormalEnemy(UnitID id, Map const& map, int pathIndex);

    int getRewardCoins() const override;

    int getEscapePenalty() const override;
};

// 快速敌人
class FastEnemy : public Enemy {
   public:
    FastEnemy(UnitID id, Map const& map, int pathIndex);

    int getRewardCoins() const override;

    int getEscapePenalty() const override;
};

// 装甲敌人
class ArmoredEnemy : public Enemy {
   public:
    ArmoredEnemy(UnitID id, Map const& map, int pathIndex);

    int getRewardCoins() const override;

    int getEscapePenalty() const override;
};