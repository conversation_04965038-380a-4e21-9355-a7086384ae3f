#pragma once

#include "TowerDefense/Types.hpp"

#include <QObject>

class Unit;

class UnitComponent {
   public:
    virtual ~UnitComponent() = default;

    virtual void initialize(Unit* owner) = 0;

    virtual void update(float deltaTime) = 0;

    [[nodiscard]] virtual ComponentType getType() const = 0;

    UnitComponent(UnitComponent const&)            = delete;
    UnitComponent& operator=(UnitComponent const&) = delete;

   protected:
    Unit* m_owner;

    explicit UnitComponent(Unit* owner = nullptr) : m_owner(owner) {}
};
