#pragma once

#include "TowerDefense/GameLogic/Enemy.hpp"
#include "TowerDefense/GameLogic/Tower.hpp"
#include "TowerDefense/Types.hpp"

#include <QPoint>
#include <QPointF>
#include <memory>

// 前向声明
class Map;

// 塔工厂 - 负责创建各种塔（使用静态方法）
class TowerFactory {
   public:
    // 创建塔的方法
    static std::shared_ptr<Tower> createTower(TowerType type, QPointF const& position, UnitID id);
};

// 敌人工厂 - 负责创建各种敌人（需要Map信息）
class EnemyFactory {
   public:
    explicit EnemyFactory(Map const& map);

    // 创建敌人的方法
    std::shared_ptr<Enemy> createEnemy(EnemyType type, int pathIndex, UnitID id);

   private:
    Map const* m_map;  // 敌人需要知道地图信息来在路径上移动（使用指针而不是引用）
};
