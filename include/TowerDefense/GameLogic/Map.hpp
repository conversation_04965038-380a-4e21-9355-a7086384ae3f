#pragma once

#include "TowerDefense/Types.hpp"

#include <QPointF>      // For waypoints
#include <QString>      // Required for QString const& parameters in friend functions
#include <QTextStream>  // 添加QTextStream头文件
#include <set>          // 用于std::set
#include <string>       // For loading from file
#include <vector>       // For tile data and paths

// 为QPoint添加比较运算符
struct QPointCompare {
    bool operator()(QPoint const& a, QPoint const& b) const {
        if (a.x() != b.x()) {
            return a.x() < b.x();
        }
        return a.y() < b.y();
    }
};

class Map {
   public:
    // 构造函数
    Map() = default;

    // 主要功能 - 文件加载
    // 导入地图数据从指定文件路径
    bool loadFromFile(std::string const& filePath);

    // 保存当前地图到指定文件
    bool saveToFile(std::string const& filePath) const;

    // 基本属性获取 (Getters)
    // 获取地图的宽度 (瓦片数)
    [[nodiscard]] int getWidth() const {
        return m_width;
    }
    // 获取地图的高度 (瓦片数)
    [[nodiscard]] int getHeight() const {
        return m_height;
    }
    // 获取每个瓦片在世界中的尺寸
    [[nodiscard]] float getTileSize() const {
        return m_tileSize;
    }

    // 获取地图中定义的路径数量
    [[nodiscard]] size_t getPathCount() const {
        return m_paths.size();
    }

    // 坐标转换函数
    // 将网格坐标 (x, y) 转换为世界位置（例如，瓦片中心）
    [[nodiscard]] QPointF gridToWorld(int x, int y) const;

    // 将世界位置转换为网格坐标
    [[nodiscard]] QPoint worldToGrid(QPointF const& worldPos) const;

    // 瓦片数据访问/查询函数
    // 获取特定网格坐标 (x, y) 的瓦片类型
    [[nodiscard]] TileType getTileType(int x, int y) const;

    // 检查网格坐标 (x, y) 是否在地图边界内
    [[nodiscard]] bool isValidCoordinate(int x, int y) const;

    // 检查特定网格坐标 (x, y) 的瓦片是否可建造指定类型的塔
    [[nodiscard]] bool isBuildable(int x, int y, TowerType towerType) const;

    // 检查指定位置是否可以建造塔
    [[nodiscard]] bool isBuildableAt(QPoint const& position) const;

    // 路径数据访问函数
    // 获取特定索引 (pathIndex) 的路径的所有航点
    [[nodiscard]] std::vector<QPointF> const& getPath(int pathIndex = 0) const;

    // 检查某网格坐标是否被近战塔阻挡
    [[nodiscard]] bool isBlocked(int x, int y) const;

    // 设置或清除近战塔阻挡
    void setBlocked(int x, int y, bool blocked) const;

   private:
    // 地图数据解析和验证函数
    void resetMapData();                                                                             // 重置地图数据
    bool parseFileContent(QTextStream& in);                                                          // 解析文件内容
    bool parseSectionLine(QString const& section, QString const& line, QString const& trimmedLine);  // 解析节数据
    bool validateBasicProperties();                                                                  // 验证基本地图属性
    bool validatePathConsistency();  // 验证路径和瓦片一致性

    // 路径验证辅助函数
    std::set<QPoint, QPointCompare> collectPathTilesFromMap();    // 收集地图中的所有路径瓦片
    std::set<QPoint, QPointCompare> collectWaypointsFromPaths();  // 收集所有路径中的航点
    bool checkWaypointsValidity();                                // 检查所有航点是否有效

    // 路径比较和报告函数
    bool comparePathTilesAndWaypoints(
        std::set<QPoint, QPointCompare> const& pathTilesInMap,
        std::set<QPoint, QPointCompare> const& waypointsInPaths);  // 比较路径瓦片和航点集合
    void reportMissingPathTiles(std::set<QPoint, QPointCompare> const& pathTilesInMap,
                                std::set<QPoint, QPointCompare> const& waypointsInPaths);  // 报告缺失的路径瓦片
    void reportExtraWaypoints(std::set<QPoint, QPointCompare> const& pathTilesInMap,
                              std::set<QPoint, QPointCompare> const& waypointsInPaths);  // 报告多余的航点

    // 检查指定坐标是否与路径相邻
    [[nodiscard]] bool isAdjacentToPath(int gridX, int gridY) const;

    // 验证可建造地面的有效性
    bool validateBuildableGrounds() const;

    int m_width{};       // 地图宽度（瓦片）
    int m_height{};      // 地图高度（瓦片）
    float m_tileSize{};  // 每个瓦片的大小（世界单位，例如像素）

    // 2D向量存储瓦片类型
    std::vector<std::vector<TileType>> m_tiles;

    // 路径向量，每个路径是一个航点向量
    std::vector<std::vector<QPointF>> m_paths;

    // 新增: 被近战塔阻挡的路径格子集合 (mutable 便于在 const 方法中修改)
    mutable std::set<QPoint, QPointCompare> m_blockedTiles;

    // 声明友元函数以允许访问私有成员
    friend bool parseMetadataLine(Map* map, QString const& line);
    friend bool parseTilesLine(Map* map, QString const& line);
    friend bool parsePathsLine(Map* map, QString const& line);
};