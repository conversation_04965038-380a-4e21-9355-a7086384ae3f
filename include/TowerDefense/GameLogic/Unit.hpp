#pragma once

#include "TowerDefense/GameLogic/UnitComponent.hpp"
#include "TowerDefense/Types.hpp"

#include <QPointF>
#include <memory>
#include <vector>

class Unit {
   public:
    virtual ~Unit() = default;

    virtual void update(float deltaTime) = 0;

    [[nodiscard]] virtual bool isDead() const     = 0;
    [[nodiscard]] virtual bool hasEscaped() const = 0;

    void addComponent(std::unique_ptr<UnitComponent> component);
    UnitComponent* getComponent(ComponentType type) const;
    template <typename T>
    T* getComponentAs() const;

    QPointF const& getPosition() const {
        return m_position;
    }
    void setPosition(QPointF const& position) {
        m_position = position;
    }

    int getHealth() const {
        return m_health;
    }
    void setHealth(int health) {
        m_health = health;
    }

    float getSpeed() const {
        return m_speed;
    }
    void setSpeed(float speed) {
        m_speed = speed;
    }

    UnitType getType() const {
        return m_type;
    }
    UnitID getId() const {
        return m_id;
    }

    Unit(Unit const&)            = delete;  // 后续可能可以设计复制的能力
    Unit& operator=(Unit const&) = delete;  // 后续可能可以设计赋值的能力

   protected:
    QPointF m_position;
    int m_health;
    float m_speed;
    UnitType m_type;
    UnitID m_id;

    std::vector<std::unique_ptr<UnitComponent>> m_components;

    explicit Unit(UnitID id, UnitType type, QPointF const& position, int health, float speed);
};

// 获取组件（获取派生类）
template <typename T>
T* Unit::getComponentAs() const {
    for (auto const& component : m_components) {
        T* specificComponent = dynamic_cast<T*>(component.get());
        if (specificComponent) {
            return specificComponent;
        }
    }
    return nullptr;
}
