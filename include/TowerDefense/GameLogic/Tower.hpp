#pragma once

#include "TowerDefense/GameLogic/Unit.hpp"
#include "TowerDefense/Types.hpp"

#include <QPointF>
#include <memory>
#include <vector>

// 前向声明
class Map;
class Affix;

// 塔的基类
class Tower : public Unit {
   public:
    ~Tower() override = default;

    // 更新塔的状态
    void update(float deltaTime) override;

    // 检查是否死亡
    bool isDead() const override;

    // 塔不会逃跑，所以始终返回false
    bool hasEscaped() const override {
        return false;
    }

    // 获取攻击范围
    virtual float getAttackRange() const = 0;

    // 获取攻击力
    virtual int getAttackDamage() const = 0;

    // 获取攻击间隔
    virtual float getAttackInterval() const = 0;

    // 获取建造成本
    virtual int getBuildCost() const = 0;

    // 获取出售价值
    virtual int getSellValue() const = 0;

    // 塔的核心功能：寻找目标并攻击
    virtual void findAndAttackTarget(std::vector<std::shared_ptr<Unit>> const& units) = 0;

    // 添加词缀
    bool addAffix(std::shared_ptr<Affix> const& affix);

    // 移除词缀
    bool removeAffix(unsigned int affixIndex);

    // 获取当前词缀
    std::vector<std::shared_ptr<Affix>> const& getAffixes() const;

    // 获取塔类型
    TowerType getTowerType() const {
        return m_towerType;
    }

    // 添加旋转与弹药支持
    [[nodiscard]] float getRotation() const {
        return m_rotation;
    }
    [[nodiscard]] bool isAmmoLoaded() const {
        return !m_hasAmmoSystem || m_ammoLoaded;
    }

   protected:
    // 构造函数
    Tower(UnitID id,
          TowerType towerType,
          UnitType unitType,
          QPointF const& position,
          int healthPoints,       // 健康点数
          float attackRange,      // 攻击范围
          int attackDamage,       // 攻击伤害
          float attackInterval);  // 攻击间隔

    TowerType m_towerType;                          // 塔类型
    float m_attackRange;                            // 攻击范围
    int m_attackDamage;                             // 攻击伤害
    float m_attackInterval;                         // 攻击间隔
    float m_attackCooldown{0.0F};                   // 攻击冷却
    std::vector<std::shared_ptr<Affix>> m_affixes;  // 词缀
    static constexpr int MAX_AFFIXES = 2;           // 最大词缀数量
    bool m_hasAmmoSystem{false};                    // 是否启用弹药系统（远程塔）
    bool m_ammoLoaded{true};                        // 弹药是否已装填完毕（仅当启用弹药系统时）
    bool m_supportRotation{false};                  // 是否支持旋转
    float m_rotation{0.0F};                         // 当前朝向角度（度）
};

// 近战塔类
class MeleeTower : public Tower {
   public:
    explicit MeleeTower(UnitID id, QPointF const& position);

    // 实现抽象方法
    float getAttackRange() const override;
    int getAttackDamage() const override;
    float getAttackInterval() const override;
    int getBuildCost() const override;
    int getSellValue() const override;
    void findAndAttackTarget(std::vector<std::shared_ptr<Unit>> const& units) override;
};

// 远程弓箭塔类
class RangedArrowTower : public Tower {
   public:
    explicit RangedArrowTower(UnitID id, QPointF const& position);

    // 实现抽象方法
    float getAttackRange() const override;
    int getAttackDamage() const override;
    float getAttackInterval() const override;
    int getBuildCost() const override;
    int getSellValue() const override;
    void findAndAttackTarget(std::vector<std::shared_ptr<Unit>> const& units) override;
};

// 远程加农炮塔类
class RangedCannonTower : public Tower {
   public:
    explicit RangedCannonTower(UnitID id, QPointF const& position);

    // 实现抽象方法
    float getAttackRange() const override;
    int getAttackDamage() const override;
    float getAttackInterval() const override;
    int getBuildCost() const override;
    int getSellValue() const override;
    void findAndAttackTarget(std::vector<std::shared_ptr<Unit>> const& units) override;
};