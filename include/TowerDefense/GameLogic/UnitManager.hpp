#pragma once

#include "TowerDefense/GameLogic/UnitFactory.hpp"
#include "TowerDefense/Types.hpp"

#include <QPoint>
#include <memory>
#include <unordered_map>
#include <vector>

class Map;
class Unit;
class GameController;

class UnitManager {
   public:
    explicit UnitManager(Map const& map, GameController* gameController = nullptr);

    // 更新所有单位
    void update(float deltaTime);

    // 生成一个新的敌方单位
    std::shared_ptr<Unit> spawnUnit(EnemyType type, int pathIndex);

    // 在地图上放置一个塔
    std::shared_ptr<Unit> placeTower(TowerType type, QPoint const& position);

    // 移除指定位置的塔
    bool removeTower(QPoint const& position);

    // 获取指定位置的单位
    std::shared_ptr<Unit> getUnitAt(QPoint const& position) const;

    // 获取指定类型的所有单位
    std::vector<std::shared_ptr<Unit>> getUnitsByType(UnitType type) const;

    // 获取所有的塔
    std::vector<std::shared_ptr<Unit>> getAllTowers() const;

    // 获取所有的敌人
    std::vector<std::shared_ptr<Unit>> getAllEnemies() const;

    // 获取所有活跃单位
    std::vector<std::shared_ptr<Unit>> const& getActiveUnits() const;

    // 处理单位逃脱（到达终点）
    bool handleUnitEscape(Unit& unit);

    // 处理单位死亡
    void handleUnitDeath(Unit& unit);

    // 清空所有单位（如关卡结束或重新开始时）
    void clearAllUnits();

    // 获取指定ID的单位（如果存在）
    std::shared_ptr<Unit> getUnitById(UnitID id) const;

   private:
    // 生成唯一的单位ID
    UnitID generateUnitId();

    // 敌人攻击冷却计时 (秒)
    std::unordered_map<UnitID, float> m_enemyAttackCooldown;

    // 存储所有活跃的单位（敌人和塔）
    std::vector<std::shared_ptr<Unit>> m_activeUnits;

    // 地图指针（不持有所有权）
    Map const* m_map;

    // 最后分配的单位ID
    UnitID m_lastUnitId;

    // 敌人工厂（TowerFactory使用静态方法，不需要实例）
    std::unique_ptr<EnemyFactory> m_enemyFactory;

    // GameController 指针（不持有所有权）
    GameController* m_gameController;
};
