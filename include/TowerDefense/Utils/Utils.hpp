#pragma once

#include <QDateTime>
#include <QDebug>
#include <QString>

namespace Utils {

// Utility functions can be added here
// For example:

// Log a message with a timestamp
inline void logMessage(QString const& message) {
    qDebug() << "[" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz") << "] " << message;
}

// Other utility functions can be added as needed

}  // namespace Utils
