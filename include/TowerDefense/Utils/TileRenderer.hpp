#pragma once

#include "TowerDefense/Utils/TilesheetConfig.hpp"
#include "TowerDefense/Types.hpp"

#include <QPainter>
#include <QRect>
#include <QPoint>
#include <memory>

/**
 * @brief TileRenderer handles rendering of tiles and game elements using TilesheetConfig
 * 
 * This class provides high-level rendering methods for different game elements,
 * abstracting away the details of tile ID lookups and sprite extraction.
 */
class TileRenderer {
public:
    /**
     * @brief Construct a new TileRenderer
     * @param tilesheetConfig Shared pointer to the tilesheet configuration
     */
    explicit TileRenderer(std::shared_ptr<TilesheetConfig> tilesheetConfig);
    
    /**
     * @brief Destroy the TileRenderer
     */
    ~TileRenderer() = default;

    // Disable copy constructor and assignment operator
    TileRenderer(const TileRenderer&) = delete;
    TileRenderer& operator=(const TileRenderer&) = delete;

    /**
     * @brief Check if the renderer is ready to use
     * @return true if tilesheet config is loaded and valid
     */
    bool isReady() const;

    /**
     * @brief Render a map tile at the specified rectangle
     * @param painter QPainter to draw with
     * @param targetRect Target rectangle to draw the tile
     * @param tileType The type of tile to render
     */
    void renderMapTile(QPainter& painter, const QRect& targetRect, TileType tileType);

    /**
     * @brief Render a map tile by element ID
     * @param painter QPainter to draw with
     * @param targetRect Target rectangle to draw the tile
     * @param elementId Element ID from configuration (e.g., "buildable_ground")
     */
    void renderTileByElementId(QPainter& painter, const QRect& targetRect, const QString& elementId);

    /**
     * @brief Render a tile by tile ID directly
     * @param painter QPainter to draw with
     * @param targetRect Target rectangle to draw the tile
     * @param tileId Numeric tile ID
     */
    void renderTileById(QPainter& painter, const QRect& targetRect, int tileId);

    /**
     * @brief Render an obstacle tile (layered rendering: base + overlay)
     * @param painter QPainter to draw with
     * @param targetRect Target rectangle to draw the tile
     */
    void renderObstacleTile(QPainter& painter, const QRect& targetRect);

    /**
     * @brief Render a number using digit sprites
     * @param painter QPainter to draw with
     * @param position Starting position for the number
     * @param number The number to render
     * @param digitSpacing Spacing between digits (default: tile size)
     */
    void renderNumber(QPainter& painter, const QPoint& position, int number, int digitSpacing = -1);

    /**
     * @brief Render a score with dollar sign prefix
     * @param painter QPainter to draw with
     * @param position Starting position
     * @param score The score value to render
     */
    void renderScore(QPainter& painter, const QPoint& position, int score);

    /**
     * @brief Get the tile size from the configuration
     * @return Tile size in pixels
     */
    int getTileSize() const;

    /**
     * @brief Get a tile pixmap for custom rendering
     * @param elementId Element ID from configuration
     * @return QPixmap containing the tile image
     */
    QPixmap getTilePixmap(const QString& elementId) const;

    /**
     * @brief Get a tile pixmap by ID for custom rendering
     * @param tileId Numeric tile ID
     * @return QPixmap containing the tile image
     */
    QPixmap getTilePixmapById(int tileId) const;

private:
    /**
     * @brief Get element ID string for a tile type
     * @param tileType The tile type enum
     * @return Element ID string, or empty string if not found
     */
    QString getElementIdForTileType(TileType tileType) const;

    /**
     * @brief Render a single digit
     * @param painter QPainter to draw with
     * @param position Position to draw the digit
     * @param digit Single digit (0-9)
     */
    void renderDigit(QPainter& painter, const QPoint& position, int digit);

private:
    std::shared_ptr<TilesheetConfig> m_tilesheetConfig;
};
