#pragma once

#include <QPixmap>
#include <QRect>
#include <QPoint>
#include <QString>
#include <QJsonObject>
#include <QJsonDocument>
#include <unordered_map>
#include <memory>

/**
 * @brief TilesheetConfig class manages loading and accessing sprite tiles from a tilesheet
 * 
 * This class loads configuration from tilesheet_simplified_config.json and provides
 * methods to access individual tiles by ID or name. It handles automatic coordinate
 * calculation based on tile IDs using the formula: gridX = tile_id % 23, gridY = tile_id / 23
 */
class TilesheetConfig {
public:
    /**
     * @brief Construct a new TilesheetConfig object
     * @param tilesheetPath Path to the tilesheet PNG file
     * @param configPath Path to the JSON configuration file (optional)
     */
    explicit TilesheetConfig(const QString& tilesheetPath, 
                           const QString& configPath = "assets/data/tilesheet_simplified_config.json");
    
    /**
     * @brief Destroy the TilesheetConfig object
     */
    ~TilesheetConfig() = default;

    // Disable copy constructor and assignment operator
    TilesheetConfig(const TilesheetConfig&) = delete;
    TilesheetConfig& operator=(const TilesheetConfig&) = delete;

    /**
     * @brief Check if the tilesheet was loaded successfully
     * @return true if loaded successfully, false otherwise
     */
    bool isLoaded() const;

    /**
     * @brief Get tile rectangle by element ID (e.g., "buildable_ground", "path")
     * @param elementId The element identifier from the JSON config
     * @return QRect representing the tile's position and size in the tilesheet
     */
    QRect getTileRect(const QString& elementId) const;

    /**
     * @brief Get tile rectangle by tile ID directly
     * @param tileId The numeric tile ID (0-298)
     * @return QRect representing the tile's position and size in the tilesheet
     */
    QRect getTileRectById(int tileId) const;

    /**
     * @brief Get tile pixmap by element ID
     * @param elementId The element identifier from the JSON config
     * @return QPixmap containing the tile image
     */
    QPixmap getTilePixmap(const QString& elementId) const;

    /**
     * @brief Get tile pixmap by tile ID directly
     * @param tileId The numeric tile ID (0-298)
     * @return QPixmap containing the tile image
     */
    QPixmap getTilePixmapById(int tileId) const;

    /**
     * @brief Get tile size (width and height are the same)
     * @return Tile size in pixels
     */
    int getTileSize() const { return m_tileSize; }

    /**
     * @brief Get grid width (number of columns in tilesheet)
     * @return Grid width
     */
    int getGridWidth() const { return m_gridWidth; }

    /**
     * @brief Get grid height (number of rows in tilesheet)
     * @return Grid height
     */
    int getGridHeight() const { return m_gridHeight; }

    /**
     * @brief Get the tile ID for a specific element
     * @param elementId The element identifier
     * @return Tile ID, or -1 if not found
     */
    int getTileId(const QString& elementId) const;

private:
    /**
     * @brief Load the tilesheet image from file
     * @param path Path to the tilesheet PNG file
     * @return true if loaded successfully
     */
    bool loadTilesheet(const QString& path);

    /**
     * @brief Load configuration from JSON file
     * @param path Path to the JSON configuration file
     * @return true if loaded successfully
     */
    bool loadConfiguration(const QString& path);

    /**
     * @brief Parse tiles section from JSON
     * @param tilesObject JSON object containing tiles configuration
     */
    void parseTilesSection(const QJsonObject& tilesObject);

    /**
     * @brief Parse UI elements section from JSON
     * @param uiObject JSON object containing UI elements configuration
     */
    void parseUIElementsSection(const QJsonObject& uiObject);

    /**
     * @brief Calculate grid position from tile ID
     * @param tileId The tile ID
     * @return QPoint containing grid coordinates
     */
    QPoint calculateGridPosition(int tileId) const;

    /**
     * @brief Calculate pixel rectangle from tile ID
     * @param tileId The tile ID
     * @return QRect containing pixel coordinates and size
     */
    QRect calculatePixelRect(int tileId) const;

    /**
     * @brief Validate tile ID is within valid range
     * @param tileId The tile ID to validate
     * @return true if valid
     */
    bool isValidTileId(int tileId) const;

private:
    // Tilesheet image
    QPixmap m_tilesheet;
    
    // Configuration parameters
    int m_tileSize = 64;
    int m_gridWidth = 23;
    int m_gridHeight = 13;
    int m_maxTileId = 298;
    
    // Element ID to tile ID mapping
    std::unordered_map<QString, int> m_tileIdMap;
    
    // Loading status
    bool m_loaded = false;
    QString m_tilesheetPath;
    QString m_configPath;
};
