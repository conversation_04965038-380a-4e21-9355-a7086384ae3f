// 该文件是所有的预定义类型和枚举类型
#pragma once
#include <cstdint>

// 游戏状态
enum class GameState : std::uint8_t {
    MAIN_MENU,        // 主菜单
    LEVEL_SELECTION,  // 关卡选择 (扩展)
    PLAYING,          // 游戏中
    PAUSED,           // 暂停
    GAME_OVER_WIN,    // 游戏胜利
    GAME_OVER_LOSE    // 游戏失败
};

// 单位类型 - 对应具体的塔和敌人类型
enum class UnitType : std::uint8_t {
    UNKNOWN,       // 未知
    TOWER_MELEE,   // 近战塔
    TOWER_RANGED,  // 远程塔
    ENEMY_MELEE    // 近战敌人
};

// 单位ID类型
using UnitID = unsigned int;

// 词缀ID类型
using AffixID = unsigned int;

// 组件类型
enum class ComponentType : std::uint8_t {
    UNKNOWN,    // 未知
    MOVEMENT,   // 移动
    ATTACK,     // 攻击
    HEALTH,     // 生命
    TARGETING,  // 目标
};

// 地图类型
enum class TileType : std::uint8_t {
    PATH,              // 路径
    BUILDABLE_GROUND,  // 可建造地面
    START_POINT,       // 起点
    END_POINT,         // 终点
    OBSTACLE,          // 障碍物
    INVALID,           // 无效类型
};

// 塔类型 - 简化为基础类型
enum class TowerType : std::uint8_t {
    MELEE,  // 近战塔
    RANGED  // 远程塔
};

// 敌人类型 - 简化为基础类型
enum class EnemyType : std::uint8_t {
    MELEE  // 近战敌人
};

// 塔词缀类型（根据素材清单.md要求）
enum class TowerAffix : std::uint8_t {
    NONE,        // 无词缀
    BERSERK,     // 狂暴的（伤害和攻击间隔大幅增加）
    ICE,         // 冰系的（攻击能使敌方单位停止攻击和移动一段时间）
    AOE_SPLASH,  // 群伤的（能同时攻击多个敌人）
    BLEEDING     // 放血的（对敌人攻击附加流血效果，使其在一定时间内持续扣血）
};

// 敌人词缀类型（根据素材清单.md要求，一个敌人最多拥有两类词缀）
enum class EnemyAffix : std::uint8_t {
    NONE,      // 无词缀
    BLINKING,  // 闪现的（能越过我方近战塔阻挡前进，有冷却时间）
    SWIFT      // 神速的（移动速度超过默认单位）
};

enum class TowerAttackType : std::uint8_t {
    MELEE,  // 近战攻击
    RANGED  // 远程攻击
};

// 动画事件类型
enum class AnimationEventType : std::uint8_t {
    TOWER_BUILT,        // 建造塔
    TOWER_DESTROYED,    // 塔被摧毁
    ENEMY_KILLED,       // 敌人被杀死
    ENEMY_ESCAPED,      // 敌人逃跑
    ENEMY_SPAWNED,      // 敌人生成
    TOWER_ATTACK,       // 塔攻击
    ENEMY_DAMAGE_TAKEN  // 敌人受到伤害
};
