#include "include/TowerDefense/Core/GameController.hpp"
#include "include/TowerDefense/GameLogic/Map.hpp"

#include <QCoreApplication>
#include <QDebug>
#include <iostream>

int main(int argc, char* argv[]) {
    QCoreApplication app(argc, argv);

    std::cout << "Testing Game Integration with ComplexSnakeMap" << std::endl;
    std::cout << "=============================================" << std::endl;

    try {
        // Test direct map loading (what we've been testing)
        std::cout << "\n1. Testing direct Map loading..." << std::endl;
        Map directMap;
        bool directSuccess = directMap.loadFromFile("assets/data/maps/ComplexSnakeMap.map");
        std::cout << "   Direct loading: " << (directSuccess ? "✓ SUCCESS" : "✗ FAILED") << std::endl;

        // Test GameController integration
        std::cout << "\n2. Testing GameController integration..." << std::endl;
        GameController& gameController = GameController::getInstance();

        // Load the ComplexSnakeMap through GameController
        gameController.startGame("assets/data/maps/ComplexSnakeMap.map");

        // Get the loaded map from GameController
        std::shared_ptr<Map> gameMap = gameController.getCurrentMap();

        if (gameMap) {
            std::cout << "   GameController loading: ✓ SUCCESS" << std::endl;
            std::cout << "   Map dimensions: " << gameMap->getWidth() << "x" << gameMap->getHeight() << std::endl;
            std::cout << "   Number of paths: " << gameMap->getPathCount() << std::endl;

            // Test some game-specific functionality
            std::cout << "\n3. Testing game-specific functionality..." << std::endl;

            // Test buildability at various positions
            bool canBuildMeleeAtStart   = gameMap->isBuildable(2, 2, TowerType::MELEE);
            bool canBuildRangedNearPath = gameMap->isBuildable(1, 2, TowerType::RANGED);

            std::cout << "   Can build melee at start (2,2): " << (canBuildMeleeAtStart ? "✓" : "✗") << std::endl;
            std::cout << "   Can build ranged near path (1,2): " << (canBuildRangedNearPath ? "✓" : "✗") << std::endl;

            // Test path access
            if (gameMap->getPathCount() > 0) {
                auto const& path = gameMap->getPath(0);
                std::cout << "   Path waypoint count: " << path.size() << std::endl;
                std::cout << "   First waypoint: (" << path[0].x() << ", " << path[0].y() << ")" << std::endl;
                std::cout << "   Last waypoint: (" << path.back().x() << ", " << path.back().y() << ")" << std::endl;
            }

            // Test coordinate conversion (important for rendering)
            QPointF worldPos  = gameMap->gridToWorld(10, 7);
            QPoint backToGrid = gameMap->worldToGrid(worldPos);
            bool conversionOk = (backToGrid.x() == 10 && backToGrid.y() == 7);
            std::cout << "   Coordinate conversion test: " << (conversionOk ? "✓" : "✗") << std::endl;

        } else {
            std::cout << "   GameController loading: ✗ FAILED" << std::endl;
            return 1;
        }

        std::cout << "\n4. Testing map save/reload cycle..." << std::endl;

        // Test saving the map (to verify it can be exported)
        bool saveSuccess = gameMap->saveToFile("test_export.map");
        std::cout << "   Map export: " << (saveSuccess ? "✓ SUCCESS" : "✗ FAILED") << std::endl;

        if (saveSuccess) {
            // Test reloading the exported map
            Map reloadedMap;
            bool reloadSuccess = reloadedMap.loadFromFile("test_export.map");
            std::cout << "   Exported map reload: " << (reloadSuccess ? "✓ SUCCESS" : "✗ FAILED") << std::endl;

            if (reloadSuccess) {
                bool dimensionsMatch = (reloadedMap.getWidth() == gameMap->getWidth()
                                        && reloadedMap.getHeight() == gameMap->getHeight());
                std::cout << "   Dimensions preserved: " << (dimensionsMatch ? "✓" : "✗") << std::endl;
            }
        }

        std::cout << "\n" << std::string(50, '=') << std::endl;
        std::cout << "✓ ComplexSnakeMap.map is fully compatible with the game!" << std::endl;
        std::cout << "✓ The map can be imported, loaded, and used by the game engine." << std::endl;
        std::cout << "✓ All game systems (pathfinding, building, rendering) can work with this map." << std::endl;
        std::cout << std::string(50, '=') << std::endl;

    } catch (std::exception const& e) {
        std::cout << "✗ Exception occurred: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "✗ Unknown exception occurred" << std::endl;
        return 1;
    }

    return 0;
}
